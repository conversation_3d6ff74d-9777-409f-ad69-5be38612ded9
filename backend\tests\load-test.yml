config:
  target: 'http://localhost:3000'
  phases:
    - duration: 60
      arrivalRate: 5
      name: "Warm up"
    - duration: 120
      arrivalRate: 10
      name: "Ramp up load"
    - duration: 300
      arrivalRate: 20
      name: "Sustained load"
  defaults:
    headers:
      Content-Type: 'application/json'
      User-Agent: 'Artillery Load Test'

scenarios:
  - name: "Health Check"
    weight: 30
    flow:
      - get:
          url: "/health"
          expect:
            - statusCode: 200

  - name: "API Info"
    weight: 20
    flow:
      - get:
          url: "/api"
          expect:
            - statusCode: 200

  - name: "Commission Rates"
    weight: 25
    flow:
      - get:
          url: "/api/commission-rates"
          headers:
            Authorization: "Bearer test-token"
          expect:
            - statusCode: [200, 401]

  - name: "Create Payment Intent"
    weight: 15
    flow:
      - post:
          url: "/api/payment-intents"
          headers:
            Authorization: "Bearer test-token"
            X-API-Key: "test-api-key"
          json:
            amount: 1000
            currency: "usd"
            vendor_id: "test-vendor-id"
            order_id: "test-order-{{ $randomString() }}"
            customer_email: "<EMAIL>"
          expect:
            - statusCode: [200, 400, 401]

  - name: "Rate Limit Test"
    weight: 10
    flow:
      - loop:
          - get:
              url: "/api"
        count: 10
