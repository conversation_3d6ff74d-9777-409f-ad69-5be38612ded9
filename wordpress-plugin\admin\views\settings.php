<?php
/**
 * Admin Settings Page
 */

if (!defined('ABSPATH')) {
    exit;
}

$plugin = StripeIntegrationPlugin::get_instance();
$options = $plugin->get_options();
$api_client = $plugin->get_api_client();
$config_status = $api_client->get_config_status();
?>

<div class="wrap">
    <h1><?php _e('Stripe Integration Settings', 'stripe-integration'); ?></h1>
    
    <?php settings_errors(); ?>
    
    <div class="stripe-integration-admin-container">
        <div class="stripe-integration-main-content">
            <form method="post" action="options.php">
                <?php
                settings_fields('stripe_integration_options');
                do_settings_sections('stripe-integration-settings');
                ?>
                
                <div class="stripe-integration-settings-section">
                    <h2><?php _e('API Configuration', 'stripe-integration'); ?></h2>
                    <p><?php _e('Configure your connection to the backend API server.', 'stripe-integration'); ?></p>
                    
                    <table class="form-table">
                        <tr>
                            <th scope="row">
                                <label for="stripe_integration_options_api_base_url">
                                    <?php _e('API Base URL', 'stripe-integration'); ?>
                                </label>
                            </th>
                            <td>
                                <input type="url" 
                                       id="stripe_integration_options_api_base_url"
                                       name="stripe_integration_options[api_base_url]" 
                                       value="<?php echo esc_attr($options['api_base_url'] ?? ''); ?>" 
                                       class="regular-text" 
                                       placeholder="https://api.yoursite.com" />
                                <p class="description">
                                    <?php _e('The base URL of your backend API server (e.g., https://api.yoursite.com)', 'stripe-integration'); ?>
                                </p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <label for="stripe_integration_options_api_key">
                                    <?php _e('API Key', 'stripe-integration'); ?>
                                </label>
                            </th>
                            <td>
                                <input type="password" 
                                       id="stripe_integration_options_api_key"
                                       name="stripe_integration_options[api_key]" 
                                       value="<?php echo esc_attr($options['api_key'] ?? ''); ?>" 
                                       class="regular-text" />
                                <p class="description">
                                    <?php _e('Your API key for authentication with the backend server', 'stripe-integration'); ?>
                                </p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <label for="stripe_integration_options_vendor_id">
                                    <?php _e('Vendor ID', 'stripe-integration'); ?>
                                </label>
                            </th>
                            <td>
                                <input type="text" 
                                       id="stripe_integration_options_vendor_id"
                                       name="stripe_integration_options[vendor_id]" 
                                       value="<?php echo esc_attr($options['vendor_id'] ?? ''); ?>" 
                                       class="regular-text" />
                                <p class="description">
                                    <?php _e('Your unique vendor ID in the payment system', 'stripe-integration'); ?>
                                </p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <label for="stripe_integration_options_niche">
                                    <?php _e('Business Niche', 'stripe-integration'); ?>
                                </label>
                            </th>
                            <td>
                                <select id="stripe_integration_options_niche" 
                                        name="stripe_integration_options[niche]">
                                    <option value="grocery" <?php selected($options['niche'] ?? '', 'grocery'); ?>>
                                        <?php _e('Grocery', 'stripe-integration'); ?>
                                    </option>
                                    <option value="catering" <?php selected($options['niche'] ?? '', 'catering'); ?>>
                                        <?php _e('Catering', 'stripe-integration'); ?>
                                    </option>
                                    <option value="restaurant" <?php selected($options['niche'] ?? '', 'restaurant'); ?>>
                                        <?php _e('Restaurant', 'stripe-integration'); ?>
                                    </option>
                                    <option value="retail" <?php selected($options['niche'] ?? '', 'retail'); ?>>
                                        <?php _e('Retail', 'stripe-integration'); ?>
                                    </option>
                                    <option value="other" <?php selected($options['niche'] ?? '', 'other'); ?>>
                                        <?php _e('Other', 'stripe-integration'); ?>
                                    </option>
                                </select>
                                <p class="description">
                                    <?php _e('Your business niche affects commission rates', 'stripe-integration'); ?>
                                </p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <label for="stripe_integration_options_test_mode">
                                    <?php _e('Test Mode', 'stripe-integration'); ?>
                                </label>
                            </th>
                            <td>
                                <input type="checkbox" 
                                       id="stripe_integration_options_test_mode"
                                       name="stripe_integration_options[test_mode]" 
                                       value="1" 
                                       <?php checked($options['test_mode'] ?? false, true); ?> />
                                <label for="stripe_integration_options_test_mode">
                                    <?php _e('Enable test mode', 'stripe-integration'); ?>
                                </label>
                                <p class="description">
                                    <?php _e('Use Stripe test mode for development and testing', 'stripe-integration'); ?>
                                </p>
                            </td>
                        </tr>
                    </table>
                </div>
                
                <?php submit_button(); ?>
            </form>
        </div>
        
        <div class="stripe-integration-sidebar">
            <div class="stripe-integration-status-card">
                <h3><?php _e('Configuration Status', 'stripe-integration'); ?></h3>
                
                <div class="stripe-integration-status-item">
                    <span class="status-label"><?php _e('API Base URL:', 'stripe-integration'); ?></span>
                    <span class="status-indicator <?php echo $config_status['api_base_url'] ? 'success' : 'error'; ?>">
                        <?php echo $config_status['api_base_url'] ? '✓' : '✗'; ?>
                    </span>
                </div>
                
                <div class="stripe-integration-status-item">
                    <span class="status-label"><?php _e('API Key:', 'stripe-integration'); ?></span>
                    <span class="status-indicator <?php echo $config_status['api_key'] ? 'success' : 'error'; ?>">
                        <?php echo $config_status['api_key'] ? '✓' : '✗'; ?>
                    </span>
                </div>
                
                <div class="stripe-integration-status-item">
                    <span class="status-label"><?php _e('Vendor ID:', 'stripe-integration'); ?></span>
                    <span class="status-indicator <?php echo $config_status['vendor_id'] ? 'success' : 'error'; ?>">
                        <?php echo $config_status['vendor_id'] ? '✓' : '✗'; ?>
                    </span>
                </div>
                
                <div class="stripe-integration-status-item">
                    <span class="status-label"><?php _e('Business Niche:', 'stripe-integration'); ?></span>
                    <span class="status-indicator <?php echo $config_status['niche'] ? 'success' : 'error'; ?>">
                        <?php echo $config_status['niche'] ? '✓' : '✗'; ?>
                    </span>
                </div>
                
                <div class="stripe-integration-status-overall">
                    <strong>
                        <?php if ($config_status['is_complete']): ?>
                            <span class="status-indicator success">✓</span>
                            <?php _e('Configuration Complete', 'stripe-integration'); ?>
                        <?php else: ?>
                            <span class="status-indicator error">✗</span>
                            <?php _e('Configuration Incomplete', 'stripe-integration'); ?>
                        <?php endif; ?>
                    </strong>
                </div>
                
                <div class="stripe-integration-test-buttons">
                    <button type="button" 
                            id="test-api-connection" 
                            class="button button-secondary"
                            <?php echo !$config_status['is_complete'] ? 'disabled' : ''; ?>>
                        <?php _e('Test API Connection', 'stripe-integration'); ?>
                    </button>
                    
                    <button type="button" 
                            id="validate-api-key" 
                            class="button button-secondary"
                            <?php echo !$config_status['api_key'] ? 'disabled' : ''; ?>>
                        <?php _e('Validate API Key', 'stripe-integration'); ?>
                    </button>
                    
                    <button type="button" 
                            id="get-vendor-status" 
                            class="button button-secondary"
                            <?php echo !$config_status['vendor_id'] ? 'disabled' : ''; ?>>
                        <?php _e('Check Vendor Status', 'stripe-integration'); ?>
                    </button>
                </div>
                
                <div id="test-results" class="stripe-integration-test-results"></div>
            </div>
            
            <div class="stripe-integration-help-card">
                <h3><?php _e('Need Help?', 'stripe-integration'); ?></h3>
                <p><?php _e('Follow these steps to configure the plugin:', 'stripe-integration'); ?></p>
                <ol>
                    <li><?php _e('Set up your backend API server', 'stripe-integration'); ?></li>
                    <li><?php _e('Generate an API key from your backend', 'stripe-integration'); ?></li>
                    <li><?php _e('Create a vendor account in the system', 'stripe-integration'); ?></li>
                    <li><?php _e('Enter your configuration details above', 'stripe-integration'); ?></li>
                    <li><?php _e('Test the connection using the buttons', 'stripe-integration'); ?></li>
                </ol>
            </div>
        </div>
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    // Test API Connection
    $('#test-api-connection').on('click', function() {
        var button = $(this);
        var resultsDiv = $('#test-results');
        
        button.prop('disabled', true).text('<?php _e('Testing...', 'stripe-integration'); ?>');
        
        $.ajax({
            url: stripe_integration_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'stripe_integration_test_connection',
                nonce: stripe_integration_admin.nonce
            },
            success: function(response) {
                if (response.success) {
                    resultsDiv.html('<div class="notice notice-success"><p>' + response.data.message + '</p></div>');
                } else {
                    resultsDiv.html('<div class="notice notice-error"><p>' + response.data.message + '</p></div>');
                }
            },
            error: function() {
                resultsDiv.html('<div class="notice notice-error"><p><?php _e('Connection test failed', 'stripe-integration'); ?></p></div>');
            },
            complete: function() {
                button.prop('disabled', false).text('<?php _e('Test API Connection', 'stripe-integration'); ?>');
            }
        });
    });
    
    // Validate API Key
    $('#validate-api-key').on('click', function() {
        var button = $(this);
        var resultsDiv = $('#test-results');
        
        button.prop('disabled', true).text('<?php _e('Validating...', 'stripe-integration'); ?>');
        
        $.ajax({
            url: stripe_integration_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'stripe_integration_validate_api_key',
                nonce: stripe_integration_admin.nonce
            },
            success: function(response) {
                if (response.success) {
                    resultsDiv.html('<div class="notice notice-success"><p>' + response.data.message + '</p></div>');
                } else {
                    resultsDiv.html('<div class="notice notice-error"><p>' + response.data.message + '</p></div>');
                }
            },
            error: function() {
                resultsDiv.html('<div class="notice notice-error"><p><?php _e('API key validation failed', 'stripe-integration'); ?></p></div>');
            },
            complete: function() {
                button.prop('disabled', false).text('<?php _e('Validate API Key', 'stripe-integration'); ?>');
            }
        });
    });
    
    // Get Vendor Status
    $('#get-vendor-status').on('click', function() {
        var button = $(this);
        var resultsDiv = $('#test-results');
        
        button.prop('disabled', true).text('<?php _e('Checking...', 'stripe-integration'); ?>');
        
        $.ajax({
            url: stripe_integration_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'stripe_integration_get_vendor_status',
                nonce: stripe_integration_admin.nonce
            },
            success: function(response) {
                if (response.success) {
                    var vendor = response.data.vendor;
                    var stripe = response.data.stripe_status;
                    var html = '<div class="notice notice-success">';
                    html += '<p><strong><?php _e('Vendor Status:', 'stripe-integration'); ?></strong></p>';
                    html += '<p><?php _e('Name:', 'stripe-integration'); ?> ' + vendor.name + '</p>';
                    html += '<p><?php _e('Email:', 'stripe-integration'); ?> ' + vendor.email + '</p>';
                    html += '<p><?php _e('Niche:', 'stripe-integration'); ?> ' + vendor.niche + '</p>';
                    html += '<p><?php _e('Stripe Account:', 'stripe-integration'); ?> ' + (stripe.has_account ? '✓' : '✗') + '</p>';
                    if (stripe.has_account) {
                        html += '<p><?php _e('Charges Enabled:', 'stripe-integration'); ?> ' + (stripe.charges_enabled ? '✓' : '✗') + '</p>';
                        html += '<p><?php _e('Payouts Enabled:', 'stripe-integration'); ?> ' + (stripe.payouts_enabled ? '✓' : '✗') + '</p>';
                    }
                    html += '</div>';
                    resultsDiv.html(html);
                } else {
                    resultsDiv.html('<div class="notice notice-error"><p>' + response.data.message + '</p></div>');
                }
            },
            error: function() {
                resultsDiv.html('<div class="notice notice-error"><p><?php _e('Vendor status check failed', 'stripe-integration'); ?></p></div>');
            },
            complete: function() {
                button.prop('disabled', false).text('<?php _e('Check Vendor Status', 'stripe-integration'); ?>');
            }
        });
    });
});
</script>
