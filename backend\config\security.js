/**
 * Security Configuration for Stripe Integration API
 */

module.exports = {
    // Rate limiting configuration
    rateLimits: {
        general: {
            windowMs: 15 * 60 * 1000, // 15 minutes
            max: 100, // limit each IP to 100 requests per windowMs
            message: 'Too many requests from this IP, please try again later.',
            standardHeaders: true,
            legacyHeaders: false
        },
        payment: {
            windowMs: 15 * 60 * 1000, // 15 minutes
            max: 10, // limit each IP to 10 payment requests per windowMs
            message: 'Too many payment requests from this IP, please try again later.',
            standardHeaders: true,
            legacyHeaders: false
        },
        auth: {
            windowMs: 15 * 60 * 1000, // 15 minutes
            max: 5, // limit each IP to 5 auth requests per windowMs
            message: 'Too many authentication attempts from this IP, please try again later.',
            standardHeaders: true,
            legacyHeaders: false
        },
        webhook: {
            windowMs: 1 * 60 * 1000, // 1 minute
            max: 100, // limit each IP to 100 webhook requests per minute
            message: 'Too many webhook requests from this IP.',
            standardHeaders: true,
            legacyHeaders: false
        },
        commission: {
            windowMs: 15 * 60 * 1000, // 15 minutes
            max: 50, // limit each IP to 50 commission requests per windowMs
            message: 'Too many commission requests from this IP, please try again later.',
            standardHeaders: true,
            legacyHeaders: false
        }
    },

    // Speed limiting configuration (requests per second)
    speedLimit: {
        windowMs: 1000, // 1 second
        max: 10, // limit each IP to 10 requests per second
        message: 'Too many requests per second, please slow down.',
        standardHeaders: true,
        legacyHeaders: false
    },

    // Security headers configuration
    securityHeaders: {
        contentSecurityPolicy: {
            directives: {
                defaultSrc: ["'self'"],
                styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
                fontSrc: ["'self'", "https://fonts.gstatic.com"],
                imgSrc: ["'self'", "data:", "https:"],
                scriptSrc: ["'self'"],
                connectSrc: ["'self'", "https://api.stripe.com"],
                frameSrc: ["'none'"],
                objectSrc: ["'none'"],
                baseUri: ["'self'"],
                formAction: ["'self'"],
                frameAncestors: ["'none'"]
            }
        },
        crossOriginEmbedderPolicy: false,
        crossOriginOpenerPolicy: false,
        crossOriginResourcePolicy: { policy: "cross-origin" },
        dnsPrefetchControl: { allow: false },
        frameguard: { action: 'deny' },
        hidePoweredBy: true,
        hsts: {
            maxAge: 31536000,
            includeSubDomains: true,
            preload: true
        },
        ieNoOpen: true,
        noSniff: true,
        originAgentCluster: true,
        permittedCrossDomainPolicies: false,
        referrerPolicy: { policy: "no-referrer" },
        xssFilter: true
    },

    // Input validation configuration
    validation: {
        maxRequestSize: '1mb',
        maxFieldSize: '1mb',
        maxFields: 100,
        maxFiles: 10,
        allowedFileTypes: ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'],
        maxFileSize: 5 * 1024 * 1024, // 5MB
        
        // Payment validation rules
        payment: {
            amount: {
                min: 0.01,
                max: 999999.99,
                precision: 2
            },
            currency: {
                allowed: ['usd', 'eur', 'gbp', 'cad', 'aud']
            },
            orderIdPattern: /^[a-zA-Z0-9\-_]{1,50}$/,
            customerNamePattern: /^[a-zA-Z\s\'-]{1,100}$/,
            emailMaxLength: 254,
            descriptionMaxLength: 500
        },

        // Vendor validation rules
        vendor: {
            namePattern: /^[a-zA-Z0-9\s\'-\.]{1,100}$/,
            nicheAllowed: ['grocery', 'catering', 'restaurant', 'retail', 'other'],
            countryPattern: /^[A-Z]{2}$/,
            businessTypeAllowed: ['individual', 'company']
        },

        // Commission validation rules
        commission: {
            rateMin: 0.001,
            rateMax: 0.5,
            amountMin: 0,
            amountMax: 999999.99
        }
    },

    // Security monitoring configuration
    monitoring: {
        // Fraud detection thresholds
        fraud: {
            highAmountThreshold: 10000,
            lowAmountThreshold: 0.01,
            rapidPaymentCount: 3,
            rapidPaymentWindow: 5 * 60 * 1000, // 5 minutes
            ipPaymentLimit: 10,
            ipPaymentWindow: 60 * 60 * 1000 // 1 hour
        },

        // API abuse detection
        abuse: {
            requestCountThreshold: 100,
            requestWindow: 60 * 60 * 1000, // 1 hour
            endpointRequestThreshold: 50,
            suspiciousPatterns: [
                /script.*?>/i,
                /javascript:/i,
                /vbscript:/i,
                /onload=/i,
                /onerror=/i,
                /eval\(/i,
                /union.*select/i,
                /drop.*table/i,
                /insert.*into/i,
                /update.*set/i,
                /delete.*from/i,
                /<iframe/i,
                /<object/i,
                /<embed/i
            ]
        },

        // Bot detection patterns
        botPatterns: [
            /bot/i,
            /crawler/i,
            /spider/i,
            /scanner/i,
            /curl/i,
            /wget/i,
            /python/i,
            /perl/i
        ],

        // Security event risk levels
        riskLevels: {
            LOW: 'low',
            MEDIUM: 'medium',
            HIGH: 'high',
            CRITICAL: 'critical'
        }
    },

    // Environment validation requirements
    requiredEnvVars: [
        'NODE_ENV',
        'PORT',
        'SUPABASE_URL',
        'SUPABASE_SERVICE_ROLE_KEY',
        'STRIPE_SECRET_KEY',
        'STRIPE_WEBHOOK_SECRET',
        'JWT_SECRET',
        'API_SECRET_KEY'
    ],

    // Optional environment variables with defaults
    optionalEnvVars: {
        CORS_ORIGIN: 'http://localhost:3000',
        RATE_LIMIT_WINDOW_MS: '900000', // 15 minutes
        RATE_LIMIT_MAX_REQUESTS: '100',
        SECURITY_WEBHOOK_URL: null,
        LOG_LEVEL: 'info',
        ENABLE_REQUEST_LOGGING: 'true',
        ENABLE_SECURITY_HEADERS: 'true',
        ENABLE_RATE_LIMITING: 'true'
    },

    // IP whitelist for admin operations
    ipWhitelist: {
        enabled: process.env.ENABLE_IP_WHITELIST === 'true',
        allowedIPs: process.env.ALLOWED_IPS ? process.env.ALLOWED_IPS.split(',') : [],
        adminEndpoints: [
            '/api/admin',
            '/api/config',
            '/api/logs'
        ]
    },

    // Request signature validation
    signatureValidation: {
        enabled: process.env.ENABLE_SIGNATURE_VALIDATION === 'true',
        algorithm: 'sha256',
        headerName: 'X-Signature',
        secret: process.env.WEBHOOK_SECRET || process.env.API_SECRET_KEY,
        tolerance: 300 // 5 minutes tolerance for timestamp
    },

    // Session security
    session: {
        name: 'stripe-integration-session',
        secret: process.env.SESSION_SECRET || process.env.JWT_SECRET,
        resave: false,
        saveUninitialized: false,
        cookie: {
            secure: process.env.NODE_ENV === 'production',
            httpOnly: true,
            maxAge: 24 * 60 * 60 * 1000, // 24 hours
            sameSite: 'strict'
        }
    },

    // CORS configuration
    cors: {
        origin: function (origin, callback) {
            const allowedOrigins = process.env.CORS_ORIGIN ? 
                process.env.CORS_ORIGIN.split(',') : 
                ['http://localhost:3000'];
            
            // Allow requests with no origin (mobile apps, etc.)
            if (!origin) return callback(null, true);
            
            if (allowedOrigins.indexOf(origin) !== -1) {
                callback(null, true);
            } else {
                callback(new Error('Not allowed by CORS'));
            }
        },
        credentials: true,
        optionsSuccessStatus: 200,
        methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
        allowedHeaders: [
            'Origin',
            'X-Requested-With',
            'Content-Type',
            'Accept',
            'Authorization',
            'X-API-Key',
            'X-Signature'
        ]
    },

    // Encryption settings
    encryption: {
        algorithm: 'aes-256-gcm',
        keyLength: 32,
        ivLength: 16,
        tagLength: 16
    }
};
