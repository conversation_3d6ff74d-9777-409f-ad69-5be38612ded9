<?php
/**
 * Payment Handler for Stripe Integration
 */

if (!defined('ABSPATH')) {
    exit;
}

class Stripe_Integration_Payment_Handler {
    
    /**
     * API client instance
     */
    private $api_client;
    
    /**
     * Constructor
     */
    public function __construct($api_client) {
        $this->api_client = $api_client;
    }
    
    /**
     * Create payment intent
     */
    public function create_payment_intent($amount, $order_id, $customer_email, $customer_name) {
        // Validate inputs
        if (empty($amount) || $amount <= 0) {
            return array(
                'success' => false,
                'message' => __('Invalid amount', 'stripe-integration'),
                'data' => null
            );
        }
        
        if (empty($order_id)) {
            return array(
                'success' => false,
                'message' => __('Order ID is required', 'stripe-integration'),
                'data' => null
            );
        }
        
        if (empty($customer_email) || !is_email($customer_email)) {
            return array(
                'success' => false,
                'message' => __('Valid email is required', 'stripe-integration'),
                'data' => null
            );
        }
        
        if (empty($customer_name)) {
            return array(
                'success' => false,
                'message' => __('Customer name is required', 'stripe-integration'),
                'data' => null
            );
        }
        
        // Check if API is configured
        if (!$this->api_client->is_configured()) {
            return array(
                'success' => false,
                'message' => __('Payment system is not configured', 'stripe-integration'),
                'data' => null
            );
        }
        
        // Create payment intent via API
        $result = $this->api_client->create_payment_intent($amount, $order_id, $customer_email, $customer_name);
        
        if ($result['success']) {
            // Log successful payment intent creation
            $this->log_payment_event('payment_intent_created', array(
                'payment_intent_id' => $result['data']['payment_intent_id'] ?? '',
                'amount' => $amount,
                'order_id' => $order_id,
                'customer_email' => $customer_email
            ));
            
            // Store payment intent in WordPress options for tracking
            $this->store_payment_intent($result['data']);
        } else {
            // Log failed payment intent creation
            $this->log_payment_event('payment_intent_failed', array(
                'amount' => $amount,
                'order_id' => $order_id,
                'customer_email' => $customer_email,
                'error' => $result['message']
            ));
        }
        
        return $result;
    }
    
    /**
     * Confirm payment
     */
    public function confirm_payment($payment_intent_id) {
        if (empty($payment_intent_id)) {
            return array(
                'success' => false,
                'message' => __('Payment intent ID is required', 'stripe-integration'),
                'data' => null
            );
        }
        
        // Confirm payment via API
        $result = $this->api_client->confirm_payment($payment_intent_id);
        
        if ($result['success']) {
            // Log successful payment confirmation
            $this->log_payment_event('payment_confirmed', array(
                'payment_intent_id' => $payment_intent_id
            ));
            
            // Update stored payment intent
            $this->update_payment_intent_status($payment_intent_id, 'succeeded');
            
            // Trigger WordPress action for successful payment
            do_action('stripe_integration_payment_succeeded', $result['data']);
        } else {
            // Log failed payment confirmation
            $this->log_payment_event('payment_confirmation_failed', array(
                'payment_intent_id' => $payment_intent_id,
                'error' => $result['message']
            ));
            
            // Update stored payment intent
            $this->update_payment_intent_status($payment_intent_id, 'failed');
            
            // Trigger WordPress action for failed payment
            do_action('stripe_integration_payment_failed', $payment_intent_id, $result['message']);
        }
        
        return $result;
    }
    
    /**
     * Cancel payment
     */
    public function cancel_payment($payment_intent_id, $reason = '') {
        if (empty($payment_intent_id)) {
            return array(
                'success' => false,
                'message' => __('Payment intent ID is required', 'stripe-integration'),
                'data' => null
            );
        }
        
        // Cancel payment via API
        $result = $this->api_client->cancel_payment($payment_intent_id, $reason);
        
        if ($result['success']) {
            // Log payment cancellation
            $this->log_payment_event('payment_canceled', array(
                'payment_intent_id' => $payment_intent_id,
                'reason' => $reason
            ));
            
            // Update stored payment intent
            $this->update_payment_intent_status($payment_intent_id, 'canceled');
            
            // Trigger WordPress action for canceled payment
            do_action('stripe_integration_payment_canceled', $payment_intent_id, $reason);
        }
        
        return $result;
    }
    
    /**
     * Get transaction details
     */
    public function get_transaction($payment_intent_id) {
        return $this->api_client->get_transaction($payment_intent_id);
    }
    
    /**
     * Store payment intent in WordPress options
     */
    private function store_payment_intent($payment_data) {
        $payment_intents = get_option('stripe_integration_payment_intents', array());
        
        $payment_intents[$payment_data['payment_intent_id']] = array(
            'payment_intent_id' => $payment_data['payment_intent_id'],
            'amount' => $payment_data['amount'],
            'commission_amount' => $payment_data['commission_amount'],
            'final_amount' => $payment_data['final_amount'],
            'order_id' => $payment_data['order_id'],
            'customer_email' => $payment_data['customer_email'],
            'customer_name' => $payment_data['customer_name'],
            'status' => 'pending',
            'created_at' => current_time('mysql'),
            'updated_at' => current_time('mysql')
        );
        
        update_option('stripe_integration_payment_intents', $payment_intents);
    }
    
    /**
     * Update payment intent status
     */
    private function update_payment_intent_status($payment_intent_id, $status) {
        $payment_intents = get_option('stripe_integration_payment_intents', array());
        
        if (isset($payment_intents[$payment_intent_id])) {
            $payment_intents[$payment_intent_id]['status'] = $status;
            $payment_intents[$payment_intent_id]['updated_at'] = current_time('mysql');
            
            update_option('stripe_integration_payment_intents', $payment_intents);
        }
    }
    
    /**
     * Get stored payment intents
     */
    public function get_stored_payment_intents($limit = 50) {
        $payment_intents = get_option('stripe_integration_payment_intents', array());
        
        // Sort by created_at descending
        uasort($payment_intents, function($a, $b) {
            return strtotime($b['created_at']) - strtotime($a['created_at']);
        });
        
        // Limit results
        return array_slice($payment_intents, 0, $limit, true);
    }
    
    /**
     * Clean up old payment intents (older than 30 days)
     */
    public function cleanup_old_payment_intents() {
        $payment_intents = get_option('stripe_integration_payment_intents', array());
        $cutoff_date = date('Y-m-d H:i:s', strtotime('-30 days'));
        
        foreach ($payment_intents as $id => $intent) {
            if ($intent['created_at'] < $cutoff_date) {
                unset($payment_intents[$id]);
            }
        }
        
        update_option('stripe_integration_payment_intents', $payment_intents);
    }
    
    /**
     * Log payment events
     */
    private function log_payment_event($event, $data) {
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('Stripe Integration Payment Event: ' . $event . ' Data: ' . json_encode($data));
        }
        
        // Store in WordPress options for admin viewing
        $logs = get_option('stripe_integration_payment_logs', array());
        
        $logs[] = array(
            'event' => $event,
            'data' => $data,
            'timestamp' => current_time('mysql'),
            'user_id' => get_current_user_id(),
            'ip_address' => $this->get_client_ip()
        );
        
        // Keep only last 100 logs
        if (count($logs) > 100) {
            $logs = array_slice($logs, -100);
        }
        
        update_option('stripe_integration_payment_logs', $logs);
    }
    
    /**
     * Get client IP address
     */
    private function get_client_ip() {
        $ip_keys = array('HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR');
        
        foreach ($ip_keys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                foreach (explode(',', $_SERVER[$key]) as $ip) {
                    $ip = trim($ip);
                    if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                        return $ip;
                    }
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    }
    
    /**
     * Get payment logs
     */
    public function get_payment_logs($limit = 50) {
        $logs = get_option('stripe_integration_payment_logs', array());
        
        // Sort by timestamp descending
        usort($logs, function($a, $b) {
            return strtotime($b['timestamp']) - strtotime($a['timestamp']);
        });
        
        return array_slice($logs, 0, $limit);
    }
    
    /**
     * Clear payment logs
     */
    public function clear_payment_logs() {
        delete_option('stripe_integration_payment_logs');
    }
}
