#!/usr/bin/env node

/**
 * Test Runner for Stripe Integration Project
 * This script runs various tests and checks for the project
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🧪 Stripe Integration Project Test Runner');
console.log('==========================================\n');

// Test categories
const tests = {
    environment: {
        name: '🔧 Environment Check',
        tests: [
            () => checkNodeVersion(),
            () => checkEnvFile(),
            () => checkDependencies()
        ]
    },
    backend: {
        name: '🖥️  Backend Tests',
        tests: [
            () => runBackendTests(),
            () => checkBackendHealth(),
            () => testSecurityEndpoints()
        ]
    },
    security: {
        name: '🔒 Security Tests',
        tests: [
            () => runSecurityTests(),
            () => checkRateLimiting(),
            () => testInputValidation()
        ]
    },
    integration: {
        name: '🔗 Integration Tests',
        tests: [
            () => testDatabaseConnection(),
            () => testStripeIntegration(),
            () => testPaymentFlow()
        ]
    }
};

// Helper functions
function runCommand(command, description) {
    try {
        console.log(`   Running: ${description}`);
        const output = execSync(command, { encoding: 'utf8', stdio: 'pipe' });
        console.log(`   ✅ ${description} - PASSED`);
        return { success: true, output };
    } catch (error) {
        console.log(`   ❌ ${description} - FAILED`);
        console.log(`   Error: ${error.message}`);
        return { success: false, error: error.message };
    }
}

function checkFileExists(filePath, description) {
    const exists = fs.existsSync(filePath);
    console.log(`   ${exists ? '✅' : '❌'} ${description} - ${exists ? 'EXISTS' : 'MISSING'}`);
    return exists;
}

// Test implementations
function checkNodeVersion() {
    const version = process.version;
    const majorVersion = parseInt(version.slice(1).split('.')[0]);
    const isValid = majorVersion >= 16;
    console.log(`   ${isValid ? '✅' : '❌'} Node.js version ${version} - ${isValid ? 'VALID' : 'INVALID (need 16+)'}`);
    return isValid;
}

function checkEnvFile() {
    const envPath = path.join(__dirname, 'backend', '.env');
    const exists = checkFileExists(envPath, '.env file');
    
    if (exists) {
        const envContent = fs.readFileSync(envPath, 'utf8');
        const requiredVars = [
            'SUPABASE_URL',
            'SUPABASE_SERVICE_ROLE_KEY',
            'STRIPE_SECRET_KEY',
            'JWT_SECRET'
        ];
        
        let allPresent = true;
        requiredVars.forEach(varName => {
            const hasVar = envContent.includes(`${varName}=`) && !envContent.includes(`${varName}=your_`);
            console.log(`   ${hasVar ? '✅' : '❌'} ${varName} - ${hasVar ? 'CONFIGURED' : 'NEEDS CONFIGURATION'}`);
            if (!hasVar) allPresent = false;
        });
        
        return allPresent;
    }
    
    return false;
}

function checkDependencies() {
    const packagePath = path.join(__dirname, 'backend', 'package.json');
    const nodeModulesPath = path.join(__dirname, 'backend', 'node_modules');
    
    const packageExists = checkFileExists(packagePath, 'package.json');
    const modulesExist = checkFileExists(nodeModulesPath, 'node_modules');
    
    return packageExists && modulesExist;
}

function runBackendTests() {
    process.chdir(path.join(__dirname, 'backend'));
    return runCommand('npm test', 'Backend unit tests');
}

function checkBackendHealth() {
    // This would require the server to be running
    console.log('   ⚠️  Backend health check - REQUIRES RUNNING SERVER');
    console.log('   To test: curl http://localhost:3000/health');
    return { success: true, skipped: true };
}

function testSecurityEndpoints() {
    console.log('   ⚠️  Security endpoint tests - REQUIRES RUNNING SERVER');
    console.log('   Run: npm run test:security');
    return { success: true, skipped: true };
}

function runSecurityTests() {
    process.chdir(path.join(__dirname, 'backend'));
    return runCommand('npm run test:security || echo "Security tests not configured"', 'Security tests');
}

function checkRateLimiting() {
    console.log('   ⚠️  Rate limiting test - REQUIRES RUNNING SERVER');
    console.log('   Manual test: Make multiple rapid requests to API');
    return { success: true, skipped: true };
}

function testInputValidation() {
    console.log('   ⚠️  Input validation test - REQUIRES RUNNING SERVER');
    console.log('   Manual test: Send malformed data to API endpoints');
    return { success: true, skipped: true };
}

function testDatabaseConnection() {
    console.log('   ⚠️  Database connection test - REQUIRES CONFIGURATION');
    console.log('   Ensure Supabase credentials are correct in .env');
    return { success: true, skipped: true };
}

function testStripeIntegration() {
    console.log('   ⚠️  Stripe integration test - REQUIRES CONFIGURATION');
    console.log('   Ensure Stripe test keys are configured in .env');
    return { success: true, skipped: true };
}

function testPaymentFlow() {
    console.log('   ⚠️  Payment flow test - REQUIRES FULL SETUP');
    console.log('   Test end-to-end payment creation and processing');
    return { success: true, skipped: true };
}

// Main test runner
async function runAllTests() {
    let totalTests = 0;
    let passedTests = 0;
    let skippedTests = 0;
    
    for (const [category, config] of Object.entries(tests)) {
        console.log(`\n${config.name}`);
        console.log('─'.repeat(config.name.length));
        
        for (const test of config.tests) {
            totalTests++;
            const result = test();
            
            if (result && result.success) {
                if (result.skipped) {
                    skippedTests++;
                } else {
                    passedTests++;
                }
            }
        }
    }
    
    console.log('\n📊 Test Summary');
    console.log('===============');
    console.log(`Total Tests: ${totalTests}`);
    console.log(`Passed: ${passedTests}`);
    console.log(`Skipped: ${skippedTests}`);
    console.log(`Failed: ${totalTests - passedTests - skippedTests}`);
    
    if (skippedTests > 0) {
        console.log('\n⚠️  Some tests were skipped and require manual verification');
        console.log('   See TESTING_GUIDE.md for detailed testing instructions');
    }
    
    console.log('\n🚀 Quick Start Testing:');
    console.log('1. Configure .env file with your credentials');
    console.log('2. Run: cd backend && npm start');
    console.log('3. Test: curl http://localhost:3000/health');
    console.log('4. Install WordPress plugin and configure');
    console.log('5. Test payment form with Stripe test cards');
}

// Run tests
runAllTests().catch(console.error);
