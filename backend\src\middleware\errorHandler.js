/**
 * Global error handling middleware
 * Handles all errors and sends appropriate responses
 */
function errorHandler(err, req, res, next) {
  // Log error details
  console.error('Error occurred:', {
    message: err.message,
    stack: err.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    timestamp: new Date().toISOString()
  });
  
  // Default error response
  let statusCode = 500;
  let errorResponse = {
    error: 'Internal Server Error',
    message: 'An unexpected error occurred',
    timestamp: new Date().toISOString()
  };
  
  // Handle specific error types
  if (err.name === 'ValidationError') {
    statusCode = 400;
    errorResponse = {
      error: 'Validation Error',
      message: err.message,
      details: err.details || []
    };
  } else if (err.name === 'UnauthorizedError' || err.message.includes('unauthorized')) {
    statusCode = 401;
    errorResponse = {
      error: 'Unauthorized',
      message: 'Authentication required or invalid credentials'
    };
  } else if (err.name === 'ForbiddenError' || err.message.includes('forbidden')) {
    statusCode = 403;
    errorResponse = {
      error: 'Forbidden',
      message: 'Insufficient permissions to access this resource'
    };
  } else if (err.name === 'NotFoundError' || err.message.includes('not found')) {
    statusCode = 404;
    errorResponse = {
      error: 'Not Found',
      message: 'The requested resource was not found'
    };
  } else if (err.name === 'ConflictError' || err.message.includes('conflict')) {
    statusCode = 409;
    errorResponse = {
      error: 'Conflict',
      message: 'The request conflicts with the current state of the resource'
    };
  } else if (err.name === 'RateLimitError' || err.message.includes('rate limit')) {
    statusCode = 429;
    errorResponse = {
      error: 'Rate Limit Exceeded',
      message: 'Too many requests. Please try again later.'
    };
  }
  
  // Handle Stripe errors
  if (err.type && err.type.startsWith('Stripe')) {
    statusCode = err.statusCode || 400;
    errorResponse = {
      error: 'Payment Error',
      message: err.message,
      type: err.type,
      code: err.code
    };
  }
  
  // Handle Supabase errors
  if (err.code && typeof err.code === 'string') {
    if (err.code.startsWith('PGRST')) {
      statusCode = 400;
      errorResponse = {
        error: 'Database Error',
        message: 'Database operation failed',
        code: err.code
      };
    }
  }
  
  // Handle JWT errors
  if (err.name === 'JsonWebTokenError') {
    statusCode = 401;
    errorResponse = {
      error: 'Invalid Token',
      message: 'The provided token is invalid'
    };
  } else if (err.name === 'TokenExpiredError') {
    statusCode = 401;
    errorResponse = {
      error: 'Token Expired',
      message: 'The provided token has expired'
    };
  }
  
  // Handle custom application errors
  if (err.statusCode) {
    statusCode = err.statusCode;
    errorResponse.error = err.name || errorResponse.error;
    errorResponse.message = err.message;
  }
  
  // Don't expose sensitive information in production
  if (process.env.NODE_ENV === 'production') {
    // Remove stack trace and sensitive details
    delete errorResponse.stack;
    
    // Generic message for 500 errors
    if (statusCode === 500) {
      errorResponse.message = 'An unexpected error occurred';
    }
  } else {
    // Include stack trace in development
    errorResponse.stack = err.stack;
  }
  
  // Send error response
  res.status(statusCode).json(errorResponse);
}

/**
 * Custom error classes for better error handling
 */
class AppError extends Error {
  constructor(message, statusCode = 500, name = 'AppError') {
    super(message);
    this.name = name;
    this.statusCode = statusCode;
    this.isOperational = true;
    
    Error.captureStackTrace(this, this.constructor);
  }
}

class ValidationError extends AppError {
  constructor(message, details = []) {
    super(message, 400, 'ValidationError');
    this.details = details;
  }
}

class UnauthorizedError extends AppError {
  constructor(message = 'Unauthorized access') {
    super(message, 401, 'UnauthorizedError');
  }
}

class ForbiddenError extends AppError {
  constructor(message = 'Forbidden access') {
    super(message, 403, 'ForbiddenError');
  }
}

class NotFoundError extends AppError {
  constructor(message = 'Resource not found') {
    super(message, 404, 'NotFoundError');
  }
}

class ConflictError extends AppError {
  constructor(message = 'Resource conflict') {
    super(message, 409, 'ConflictError');
  }
}

class RateLimitError extends AppError {
  constructor(message = 'Rate limit exceeded') {
    super(message, 429, 'RateLimitError');
  }
}

/**
 * Async error wrapper to catch async errors in route handlers
 * @param {Function} fn - Async function to wrap
 * @returns {Function} - Wrapped function
 */
function asyncHandler(fn) {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
}

module.exports = {
  errorHandler,
  AppError,
  ValidationError,
  UnauthorizedError,
  ForbiddenError,
  NotFoundError,
  ConflictError,
  RateLimitError,
  asyncHandler
};
