const { supabase } = require('../config/database');
const { NotFoundError, ValidationError } = require('../middleware/errorHandler');

/**
 * Commission calculation and management service
 */
class CommissionService {
  /**
   * Calculate commission for a transaction
   * @param {Object} params - Transaction parameters
   * @param {number} params.amount - Transaction amount
   * @param {string} params.niche - Business niche
   * @param {string} params.vendor_id - Vendor ID (optional)
   * @returns {Promise<Object>} Commission calculation result
   */
  async calculateCommission({ amount, niche, vendor_id }) {
    if (!amount || amount <= 0) {
      throw new ValidationError('Amount must be a positive number');
    }

    if (!niche) {
      throw new ValidationError('Niche is required');
    }

    // Get commission rate for the niche
    const commissionRate = await this.getCommissionRate(niche, amount);
    
    // Calculate commission amount
    const commissionAmount = amount * commissionRate.rate;
    
    // Get vendor-specific adjustments if vendor_id is provided
    let vendorAdjustment = 0;
    if (vendor_id) {
      vendorAdjustment = await this.getVendorCommissionAdjustment(vendor_id);
    }
    
    // Apply vendor adjustment
    const finalCommissionRate = Math.max(0, commissionRate.rate + vendorAdjustment);
    const finalCommissionAmount = amount * finalCommissionRate;
    
    return {
      original_amount: amount,
      commission_rate: finalCommissionRate,
      commission_amount: finalCommissionAmount,
      final_amount: amount - finalCommissionAmount,
      niche,
      commission_rule: commissionRate,
      vendor_adjustment: vendorAdjustment
    };
  }

  /**
   * Get commission rate for a specific niche and amount
   * @param {string} niche - Business niche
   * @param {number} amount - Transaction amount
   * @returns {Promise<Object>} Commission rate object
   */
  async getCommissionRate(niche, amount) {
    // First, try to get niche-specific rate
    let { data: rates, error } = await supabase
      .from('commission_rates')
      .select('*')
      .eq('niche', niche)
      .eq('is_active', true)
      .order('created_at', { ascending: false });

    if (error) {
      throw error;
    }

    // Filter rates by amount range
    const applicableRates = rates.filter(rate => {
      const minAmount = rate.min_amount || 0;
      const maxAmount = rate.max_amount || Infinity;
      return amount >= minAmount && amount <= maxAmount;
    });

    if (applicableRates.length > 0) {
      return applicableRates[0]; // Return the most recent applicable rate
    }

    // If no niche-specific rate found, try default rate
    ({ data: rates, error } = await supabase
      .from('commission_rates')
      .select('*')
      .eq('niche', 'default')
      .eq('is_active', true)
      .order('created_at', { ascending: false })
      .limit(1));

    if (error) {
      throw error;
    }

    if (rates.length > 0) {
      return rates[0];
    }

    // If no rates found, use environment default
    const defaultRate = parseFloat(process.env.DEFAULT_COMMISSION_RATE) || 0.05;
    
    return {
      id: 'default',
      niche: 'default',
      rate: defaultRate,
      min_amount: 0,
      max_amount: null,
      is_active: true,
      created_at: new Date().toISOString()
    };
  }

  /**
   * Get vendor-specific commission adjustment
   * @param {string} vendor_id - Vendor ID
   * @returns {Promise<number>} Commission adjustment (positive or negative)
   */
  async getVendorCommissionAdjustment(vendor_id) {
    try {
      const { data: vendor, error } = await supabase
        .from('vendors')
        .select('commission_adjustment, tier')
        .eq('id', vendor_id)
        .single();

      if (error || !vendor) {
        return 0; // No adjustment if vendor not found
      }

      // Return vendor-specific adjustment or tier-based adjustment
      return vendor.commission_adjustment || this.getTierAdjustment(vendor.tier) || 0;
    } catch (error) {
      console.error('Error getting vendor commission adjustment:', error);
      return 0;
    }
  }

  /**
   * Get commission adjustment based on vendor tier
   * @param {string} tier - Vendor tier (bronze, silver, gold, platinum)
   * @returns {number} Commission adjustment
   */
  getTierAdjustment(tier) {
    const tierAdjustments = {
      'bronze': 0,      // No adjustment
      'silver': -0.005, // 0.5% reduction
      'gold': -0.01,    // 1% reduction
      'platinum': -0.015 // 1.5% reduction
    };

    return tierAdjustments[tier] || 0;
  }

  /**
   * Create or update commission rate
   * @param {Object} rateData - Commission rate data
   * @returns {Promise<Object>} Created/updated commission rate
   */
  async createCommissionRate(rateData) {
    const { niche, rate, min_amount = 0, max_amount = null, is_active = true } = rateData;

    // Validate rate bounds
    const minRate = parseFloat(process.env.MIN_COMMISSION_RATE) || 0.001;
    const maxRate = parseFloat(process.env.MAX_COMMISSION_RATE) || 0.5;

    if (rate < minRate || rate > maxRate) {
      throw new ValidationError(`Commission rate must be between ${minRate * 100}% and ${maxRate * 100}%`);
    }

    // Check for existing active rate for this niche and amount range
    const { data: existingRates, error: checkError } = await supabase
      .from('commission_rates')
      .select('*')
      .eq('niche', niche)
      .eq('is_active', true);

    if (checkError) {
      throw checkError;
    }

    // Check for overlapping amount ranges
    const hasOverlap = existingRates.some(existingRate => {
      const existingMin = existingRate.min_amount || 0;
      const existingMax = existingRate.max_amount || Infinity;
      const newMin = min_amount || 0;
      const newMax = max_amount || Infinity;

      return (newMin < existingMax && newMax > existingMin);
    });

    if (hasOverlap) {
      throw new ValidationError('Commission rate range overlaps with existing rate for this niche');
    }

    // Create new commission rate
    const { data: newRate, error } = await supabase
      .from('commission_rates')
      .insert({
        niche,
        rate,
        min_amount,
        max_amount,
        is_active,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      throw error;
    }

    return newRate;
  }

  /**
   * Get commission statistics
   * @param {Object} filters - Filter options
   * @returns {Promise<Object>} Commission statistics
   */
  async getCommissionStats(filters = {}) {
    const {
      start_date,
      end_date,
      niche,
      vendor_id
    } = filters;

    let query = supabase
      .from('transactions')
      .select('commission_amount, commission_rate, amount, niche, vendor_id, created_at')
      .eq('status', 'succeeded');

    // Apply filters
    if (start_date) {
      query = query.gte('created_at', start_date);
    }
    if (end_date) {
      query = query.lte('created_at', end_date);
    }
    if (niche) {
      query = query.eq('niche', niche);
    }
    if (vendor_id) {
      query = query.eq('vendor_id', vendor_id);
    }

    const { data: transactions, error } = await query;

    if (error) {
      throw error;
    }

    // Calculate statistics
    const stats = {
      total_transactions: transactions.length,
      total_commission_earned: 0,
      total_transaction_volume: 0,
      average_commission_rate: 0,
      by_niche: {},
      by_month: {}
    };

    let totalCommissionRate = 0;

    transactions.forEach(transaction => {
      const commission = parseFloat(transaction.commission_amount) || 0;
      const amount = parseFloat(transaction.amount) || 0;
      const rate = parseFloat(transaction.commission_rate) || 0;

      stats.total_commission_earned += commission;
      stats.total_transaction_volume += amount;
      totalCommissionRate += rate;

      // Group by niche
      if (!stats.by_niche[transaction.niche]) {
        stats.by_niche[transaction.niche] = {
          transaction_count: 0,
          total_commission: 0,
          total_volume: 0,
          average_rate: 0
        };
      }
      stats.by_niche[transaction.niche].transaction_count += 1;
      stats.by_niche[transaction.niche].total_commission += commission;
      stats.by_niche[transaction.niche].total_volume += amount;

      // Group by month
      const month = new Date(transaction.created_at).toISOString().substring(0, 7);
      if (!stats.by_month[month]) {
        stats.by_month[month] = {
          transaction_count: 0,
          total_commission: 0,
          total_volume: 0
        };
      }
      stats.by_month[month].transaction_count += 1;
      stats.by_month[month].total_commission += commission;
      stats.by_month[month].total_volume += amount;
    });

    // Calculate averages
    stats.average_commission_rate = stats.total_transactions > 0 
      ? totalCommissionRate / stats.total_transactions 
      : 0;

    // Calculate average rates by niche
    Object.keys(stats.by_niche).forEach(niche => {
      const nicheStats = stats.by_niche[niche];
      nicheStats.average_rate = nicheStats.total_volume > 0 
        ? nicheStats.total_commission / nicheStats.total_volume 
        : 0;
    });

    return stats;
  }
}

module.exports = new CommissionService();
