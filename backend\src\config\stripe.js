const Stripe = require('stripe');

// Initialize Stripe with secret key
const stripe = Stripe(process.env.STRIPE_SECRET_KEY);

if (!process.env.STRIPE_SECRET_KEY) {
  throw new Error('STRIPE_SECRET_KEY is required in environment variables');
}

/**
 * Stripe configuration and helper functions
 */
const stripeConfig = {
  // Webhook endpoint secret for verifying webhook signatures
  webhookSecret: process.env.STRIPE_WEBHOOK_SECRET,
  
  // Publishable key for frontend integration
  publishableKey: process.env.STRIPE_PUBLISHABLE_KEY,
  
  // Default currency
  defaultCurrency: 'usd',
  
  // Stripe Connect configuration
  connect: {
    // Express account type for vendors
    accountType: 'express',
    
    // Required capabilities for Express accounts
    capabilities: {
      card_payments: { requested: true },
      transfers: { requested: true }
    },
    
    // Business type
    businessType: 'individual',
    
    // Default country
    country: 'US'
  },
  
  // Payment intent configuration
  paymentIntent: {
    // Automatic payment methods
    automaticPaymentMethods: {
      enabled: true
    },
    
    // Capture method
    captureMethod: 'automatic',
    
    // Confirmation method
    confirmationMethod: 'automatic'
  }
};

/**
 * Create a Stripe Express account for a vendor
 * @param {Object} vendorData - Vendor information
 * @returns {Promise<Object>} - Stripe account object
 */
async function createExpressAccount(vendorData) {
  try {
    const account = await stripe.accounts.create({
      type: stripeConfig.connect.accountType,
      country: vendorData.country || stripeConfig.connect.country,
      email: vendorData.email,
      capabilities: stripeConfig.connect.capabilities,
      business_type: stripeConfig.connect.businessType,
      metadata: {
        vendor_id: vendorData.vendor_id,
        niche: vendorData.niche
      }
    });
    
    return account;
  } catch (error) {
    console.error('Error creating Stripe Express account:', error);
    throw error;
  }
}

/**
 * Create an account link for onboarding
 * @param {string} accountId - Stripe account ID
 * @param {string} refreshUrl - URL to redirect if refresh is needed
 * @param {string} returnUrl - URL to redirect after successful onboarding
 * @returns {Promise<Object>} - Account link object
 */
async function createAccountLink(accountId, refreshUrl, returnUrl) {
  try {
    const accountLink = await stripe.accountLinks.create({
      account: accountId,
      refresh_url: refreshUrl,
      return_url: returnUrl,
      type: 'account_onboarding'
    });
    
    return accountLink;
  } catch (error) {
    console.error('Error creating account link:', error);
    throw error;
  }
}

/**
 * Verify webhook signature
 * @param {string} payload - Raw request body
 * @param {string} signature - Stripe signature header
 * @returns {Object} - Verified event object
 */
function verifyWebhookSignature(payload, signature) {
  try {
    const event = stripe.webhooks.constructEvent(
      payload,
      signature,
      stripeConfig.webhookSecret
    );
    return event;
  } catch (error) {
    console.error('Webhook signature verification failed:', error);
    throw error;
  }
}

/**
 * Test Stripe connection
 */
async function testStripeConnection() {
  try {
    await stripe.balance.retrieve();
    console.log('✅ Stripe connection successful');
    return true;
  } catch (error) {
    console.error('❌ Stripe connection failed:', error.message);
    return false;
  }
}

module.exports = {
  stripe,
  stripeConfig,
  createExpressAccount,
  createAccountLink,
  verifyWebhookSignature,
  testStripeConnection
};
