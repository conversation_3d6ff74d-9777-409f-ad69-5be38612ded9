<?php
/**
 * Admin Dashboard Page
 */

if (!defined('ABSPATH')) {
    exit;
}

$plugin = StripeIntegrationPlugin::get_instance();
$options = $plugin->get_options();
$api_client = $plugin->get_api_client();
$payment_handler = $plugin->get_payment_handler();

// Get statistics
$admin_settings = new Stripe_Integration_Admin_Settings();
$statistics = $admin_settings->get_statistics('30d');
$config_status = $api_client->get_config_status();

// Get recent transactions
$recent_transactions = $payment_handler->get_stored_payment_intents(10);
?>

<div class="wrap">
    <h1><?php _e('Stripe Integration Dashboard', 'stripe-integration'); ?></h1>
    
    <div class="stripe-integration-dashboard">
        
        <!-- Configuration Status -->
        <div class="stripe-integration-dashboard-section">
            <h2><?php _e('Configuration Status', 'stripe-integration'); ?></h2>
            
            <?php if ($config_status['is_complete']): ?>
                <div class="notice notice-success">
                    <p><strong><?php _e('✓ Plugin is properly configured and ready to accept payments!', 'stripe-integration'); ?></strong></p>
                </div>
            <?php else: ?>
                <div class="notice notice-warning">
                    <p><strong><?php _e('⚠ Plugin configuration is incomplete.', 'stripe-integration'); ?></strong></p>
                    <p>
                        <a href="<?php echo admin_url('admin.php?page=stripe-integration-settings'); ?>" class="button button-primary">
                            <?php _e('Complete Configuration', 'stripe-integration'); ?>
                        </a>
                    </p>
                </div>
            <?php endif; ?>
        </div>
        
        <!-- Statistics -->
        <div class="stripe-integration-dashboard-section">
            <h2><?php _e('Payment Statistics (Last 30 Days)', 'stripe-integration'); ?></h2>
            
            <div class="stripe-integration-stats-grid">
                <div class="stat-card">
                    <div class="stat-number"><?php echo esc_html($statistics['total_transactions'] ?? 0); ?></div>
                    <div class="stat-label"><?php _e('Total Transactions', 'stripe-integration'); ?></div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-number">$<?php echo esc_html(number_format($statistics['total_amount'] ?? 0, 2)); ?></div>
                    <div class="stat-label"><?php _e('Total Amount', 'stripe-integration'); ?></div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-number">$<?php echo esc_html(number_format($statistics['total_commission'] ?? 0, 2)); ?></div>
                    <div class="stat-label"><?php _e('Total Commission', 'stripe-integration'); ?></div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-number"><?php echo esc_html(number_format($statistics['success_rate'] ?? 0, 1)); ?>%</div>
                    <div class="stat-label"><?php _e('Success Rate', 'stripe-integration'); ?></div>
                </div>
            </div>
        </div>
        
        <!-- Recent Transactions -->
        <div class="stripe-integration-dashboard-section">
            <h2><?php _e('Recent Transactions', 'stripe-integration'); ?></h2>
            
            <?php if (!empty($recent_transactions)): ?>
                <div class="stripe-integration-transactions-table">
                    <table class="wp-list-table widefat fixed striped">
                        <thead>
                            <tr>
                                <th><?php _e('Payment ID', 'stripe-integration'); ?></th>
                                <th><?php _e('Customer', 'stripe-integration'); ?></th>
                                <th><?php _e('Amount', 'stripe-integration'); ?></th>
                                <th><?php _e('Commission', 'stripe-integration'); ?></th>
                                <th><?php _e('Status', 'stripe-integration'); ?></th>
                                <th><?php _e('Date', 'stripe-integration'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($recent_transactions as $transaction): ?>
                                <tr>
                                    <td>
                                        <code><?php echo esc_html(substr($transaction['payment_intent_id'], 0, 20)); ?>...</code>
                                    </td>
                                    <td>
                                        <strong><?php echo esc_html($transaction['customer_name']); ?></strong><br>
                                        <small><?php echo esc_html($transaction['customer_email']); ?></small>
                                    </td>
                                    <td>
                                        $<?php echo esc_html(number_format($transaction['amount'], 2)); ?>
                                    </td>
                                    <td>
                                        $<?php echo esc_html(number_format($transaction['commission_amount'] ?? 0, 2)); ?>
                                    </td>
                                    <td>
                                        <span class="status-badge status-<?php echo esc_attr($transaction['status']); ?>">
                                            <?php echo esc_html(ucfirst($transaction['status'])); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php echo esc_html(date('M j, Y g:i A', strtotime($transaction['created_at']))); ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                
                <p>
                    <a href="<?php echo admin_url('admin.php?page=stripe-integration-transactions'); ?>" class="button">
                        <?php _e('View All Transactions', 'stripe-integration'); ?>
                    </a>
                </p>
            <?php else: ?>
                <div class="notice notice-info">
                    <p><?php _e('No transactions found. Once you start accepting payments, they will appear here.', 'stripe-integration'); ?></p>
                </div>
            <?php endif; ?>
        </div>
        
        <!-- Quick Actions -->
        <div class="stripe-integration-dashboard-section">
            <h2><?php _e('Quick Actions', 'stripe-integration'); ?></h2>
            
            <div class="stripe-integration-quick-actions">
                <a href="<?php echo admin_url('admin.php?page=stripe-integration-settings'); ?>" class="button button-primary">
                    <?php _e('Plugin Settings', 'stripe-integration'); ?>
                </a>
                
                <button type="button" id="test-payment-system" class="button button-secondary">
                    <?php _e('Test Payment System', 'stripe-integration'); ?>
                </button>
                
                <button type="button" id="view-commission-rates" class="button button-secondary">
                    <?php _e('View Commission Rates', 'stripe-integration'); ?>
                </button>
                
                <button type="button" id="clear-logs" class="button button-secondary">
                    <?php _e('Clear Payment Logs', 'stripe-integration'); ?>
                </button>
            </div>
            
            <div id="quick-action-results" class="stripe-integration-quick-action-results"></div>
        </div>
        
        <!-- Usage Instructions -->
        <div class="stripe-integration-dashboard-section">
            <h2><?php _e('How to Use', 'stripe-integration'); ?></h2>
            
            <div class="stripe-integration-usage-instructions">
                <h3><?php _e('Shortcodes', 'stripe-integration'); ?></h3>
                <p><?php _e('Use these shortcodes to add payment functionality to your pages:', 'stripe-integration'); ?></p>
                
                <div class="shortcode-example">
                    <h4><?php _e('Simple Checkout Button', 'stripe-integration'); ?></h4>
                    <code>[stripe_checkout amount="29.99" text="Buy Now" description="Product Name"]</code>
                </div>
                
                <div class="shortcode-example">
                    <h4><?php _e('Full Payment Form', 'stripe-integration'); ?></h4>
                    <code>[stripe_payment_form amount="29.99" description="Complete your purchase"]</code>
                </div>
                
                <div class="shortcode-example">
                    <h4><?php _e('Payment Form with Variable Amount', 'stripe-integration'); ?></h4>
                    <code>[stripe_payment_form show_amount_field="true" description="Enter your payment amount"]</code>
                </div>
                
                <h3><?php _e('PHP Integration', 'stripe-integration'); ?></h3>
                <p><?php _e('You can also integrate payments directly in your theme:', 'stripe-integration'); ?></p>
                
                <div class="code-example">
                    <pre><code><?php echo esc_html('<?php
// Get plugin instance
$stripe_plugin = StripeIntegrationPlugin::get_instance();

// Create payment intent
$result = $stripe_plugin->get_payment_handler()->create_payment_intent(
    29.99,           // amount
    "ORDER-123",     // order_id
    "<EMAIL>", // customer_email
    "John Doe"       // customer_name
);

if ($result["success"]) {
    // Handle success
    echo "Payment intent created: " . $result["data"]["payment_intent_id"];
} else {
    // Handle error
    echo "Error: " . $result["message"];
}
?>'); ?></code></pre>
                </div>
            </div>
        </div>
        
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    // Test Payment System
    $('#test-payment-system').on('click', function() {
        var button = $(this);
        var resultsDiv = $('#quick-action-results');
        
        button.prop('disabled', true).text('<?php _e('Testing...', 'stripe-integration'); ?>');
        
        $.ajax({
            url: stripe_integration_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'stripe_integration_test_connection',
                nonce: stripe_integration_admin.nonce
            },
            success: function(response) {
                if (response.success) {
                    resultsDiv.html('<div class="notice notice-success"><p><?php _e('Payment system is working correctly!', 'stripe-integration'); ?></p></div>');
                } else {
                    resultsDiv.html('<div class="notice notice-error"><p><?php _e('Payment system test failed:', 'stripe-integration'); ?> ' + response.data.message + '</p></div>');
                }
            },
            error: function() {
                resultsDiv.html('<div class="notice notice-error"><p><?php _e('Test failed - network error', 'stripe-integration'); ?></p></div>');
            },
            complete: function() {
                button.prop('disabled', false).text('<?php _e('Test Payment System', 'stripe-integration'); ?>');
            }
        });
    });
    
    // View Commission Rates
    $('#view-commission-rates').on('click', function() {
        var button = $(this);
        var resultsDiv = $('#quick-action-results');
        
        button.prop('disabled', true).text('<?php _e('Loading...', 'stripe-integration'); ?>');
        
        $.ajax({
            url: stripe_integration_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'stripe_integration_get_commission_rates',
                nonce: stripe_integration_admin.nonce
            },
            success: function(response) {
                if (response.success) {
                    var rates = response.data;
                    var html = '<div class="notice notice-info"><h4><?php _e('Current Commission Rates:', 'stripe-integration'); ?></h4><ul>';
                    for (var niche in rates) {
                        html += '<li><strong>' + niche + ':</strong> ' + rates[niche] + '%</li>';
                    }
                    html += '</ul></div>';
                    resultsDiv.html(html);
                } else {
                    resultsDiv.html('<div class="notice notice-error"><p>' + response.data.message + '</p></div>');
                }
            },
            error: function() {
                resultsDiv.html('<div class="notice notice-error"><p><?php _e('Failed to load commission rates', 'stripe-integration'); ?></p></div>');
            },
            complete: function() {
                button.prop('disabled', false).text('<?php _e('View Commission Rates', 'stripe-integration'); ?>');
            }
        });
    });
    
    // Clear Logs
    $('#clear-logs').on('click', function() {
        if (!confirm('<?php _e('Are you sure you want to clear all payment logs?', 'stripe-integration'); ?>')) {
            return;
        }
        
        var button = $(this);
        var resultsDiv = $('#quick-action-results');
        
        button.prop('disabled', true).text('<?php _e('Clearing...', 'stripe-integration'); ?>');
        
        $.ajax({
            url: stripe_integration_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'stripe_integration_clear_logs',
                nonce: stripe_integration_admin.nonce
            },
            success: function(response) {
                if (response.success) {
                    resultsDiv.html('<div class="notice notice-success"><p><?php _e('Payment logs cleared successfully!', 'stripe-integration'); ?></p></div>');
                } else {
                    resultsDiv.html('<div class="notice notice-error"><p>' + response.data.message + '</p></div>');
                }
            },
            error: function() {
                resultsDiv.html('<div class="notice notice-error"><p><?php _e('Failed to clear logs', 'stripe-integration'); ?></p></div>');
            },
            complete: function() {
                button.prop('disabled', false).text('<?php _e('Clear Payment Logs', 'stripe-integration'); ?>');
            }
        });
    });
});
</script>
