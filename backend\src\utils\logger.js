/**
 * Simple logging utility
 */
class Logger {
  constructor() {
    this.logLevel = process.env.LOG_LEVEL || 'info';
    this.levels = {
      error: 0,
      warn: 1,
      info: 2,
      debug: 3
    };
  }

  shouldLog(level) {
    return this.levels[level] <= this.levels[this.logLevel];
  }

  formatMessage(level, message, meta = {}) {
    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      level: level.toUpperCase(),
      message,
      ...meta
    };

    if (process.env.NODE_ENV === 'development') {
      return JSON.stringify(logEntry, null, 2);
    }
    return JSON.stringify(logEntry);
  }

  error(message, meta = {}) {
    if (this.shouldLog('error')) {
      console.error(this.formatMessage('error', message, meta));
    }
  }

  warn(message, meta = {}) {
    if (this.shouldLog('warn')) {
      console.warn(this.formatMessage('warn', message, meta));
    }
  }

  info(message, meta = {}) {
    if (this.shouldLog('info')) {
      console.log(this.formatMessage('info', message, meta));
    }
  }

  debug(message, meta = {}) {
    if (this.shouldLog('debug')) {
      console.log(this.formatMessage('debug', message, meta));
    }
  }

  // Log payment events
  logPayment(event, paymentData) {
    this.info(`Payment ${event}`, {
      payment_intent_id: paymentData.payment_intent_id,
      amount: paymentData.amount,
      vendor_id: paymentData.vendor_id,
      status: paymentData.status
    });
  }

  // Log commission events
  logCommission(event, commissionData) {
    this.info(`Commission ${event}`, {
      transaction_id: commissionData.transaction_id,
      vendor_id: commissionData.vendor_id,
      commission_amount: commissionData.commission_amount,
      commission_rate: commissionData.commission_rate
    });
  }

  // Log webhook events
  logWebhook(event, webhookData) {
    this.info(`Webhook ${event}`, {
      stripe_event_id: webhookData.id,
      stripe_event_type: webhookData.type,
      processed_at: new Date().toISOString()
    });
  }

  // Log API requests
  logApiRequest(req, res, responseTime) {
    this.info('API Request', {
      method: req.method,
      url: req.originalUrl,
      status_code: res.statusCode,
      response_time_ms: responseTime,
      user_agent: req.get('User-Agent'),
      ip: req.ip
    });
  }
}

module.exports = new Logger();
