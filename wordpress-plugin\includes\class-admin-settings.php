<?php
/**
 * Admin Settings Handler
 */

if (!defined('ABSPATH')) {
    exit;
}

class Stripe_Integration_Admin_Settings {
    
    /**
     * Plugin options
     */
    private $options;
    
    /**
     * API client
     */
    private $api_client;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->options = get_option('stripe_integration_options', array());
        
        add_action('admin_init', array($this, 'init_settings'));
        add_action('wp_ajax_stripe_integration_test_connection', array($this, 'ajax_test_connection'));
        add_action('wp_ajax_stripe_integration_validate_api_key', array($this, 'ajax_validate_api_key'));
        add_action('wp_ajax_stripe_integration_get_vendor_status', array($this, 'ajax_get_vendor_status'));
    }
    
    /**
     * Initialize settings
     */
    public function init_settings() {
        // This is handled in the main plugin class
        // We can add additional settings logic here if needed
    }
    
    /**
     * AJAX handler for testing API connection
     */
    public function ajax_test_connection() {
        check_ajax_referer('stripe_integration_admin_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }
        
        $api_client = new Stripe_Integration_API_Client($this->options);
        $result = $api_client->test_connection();
        
        if ($result['success']) {
            wp_send_json_success(array(
                'message' => __('API connection successful!', 'stripe-integration'),
                'data' => $result['data']
            ));
        } else {
            wp_send_json_error(array(
                'message' => __('API connection failed: ', 'stripe-integration') . $result['message'],
                'data' => $result['data']
            ));
        }
    }
    
    /**
     * AJAX handler for validating API key
     */
    public function ajax_validate_api_key() {
        check_ajax_referer('stripe_integration_admin_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }
        
        $api_client = new Stripe_Integration_API_Client($this->options);
        $result = $api_client->validate_api_key();
        
        if ($result['success']) {
            wp_send_json_success(array(
                'message' => __('API key is valid!', 'stripe-integration'),
                'data' => $result['data']
            ));
        } else {
            wp_send_json_error(array(
                'message' => __('API key validation failed: ', 'stripe-integration') . $result['message'],
                'data' => $result['data']
            ));
        }
    }
    
    /**
     * AJAX handler for getting vendor status
     */
    public function ajax_get_vendor_status() {
        check_ajax_referer('stripe_integration_admin_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }
        
        $api_client = new Stripe_Integration_API_Client($this->options);
        
        // Get vendor info
        $vendor_result = $api_client->get_vendor_info();
        
        // Get Stripe status
        $stripe_result = $api_client->get_vendor_stripe_status();
        
        if ($vendor_result['success'] && $stripe_result['success']) {
            wp_send_json_success(array(
                'vendor' => $vendor_result['data'],
                'stripe_status' => $stripe_result['data']
            ));
        } else {
            $error_message = '';
            if (!$vendor_result['success']) {
                $error_message .= 'Vendor: ' . $vendor_result['message'] . ' ';
            }
            if (!$stripe_result['success']) {
                $error_message .= 'Stripe: ' . $stripe_result['message'];
            }
            
            wp_send_json_error(array(
                'message' => trim($error_message)
            ));
        }
    }
    
    /**
     * Get configuration status for display
     */
    public function get_config_status() {
        $api_client = new Stripe_Integration_API_Client($this->options);
        return $api_client->get_config_status();
    }
    
    /**
     * Get commission rates for display
     */
    public function get_commission_rates() {
        $api_client = new Stripe_Integration_API_Client($this->options);
        $result = $api_client->get_commission_rates();
        
        if ($result['success']) {
            return $result['data'];
        }
        
        return array();
    }
    
    /**
     * Get statistics for dashboard
     */
    public function get_statistics($period = '30d') {
        $api_client = new Stripe_Integration_API_Client($this->options);
        $result = $api_client->get_statistics($period);
        
        if ($result['success']) {
            return $result['data'];
        }
        
        return array(
            'total_transactions' => 0,
            'total_amount' => 0,
            'total_commission' => 0,
            'success_rate' => 0
        );
    }
    
    /**
     * Render settings field helper
     */
    public function render_field($field_name, $field_config) {
        $value = isset($this->options[$field_name]) ? $this->options[$field_name] : ($field_config['default'] ?? '');
        $field_type = $field_config['type'] ?? 'text';
        $field_id = 'stripe_integration_options_' . $field_name;
        $field_name_attr = 'stripe_integration_options[' . $field_name . ']';
        
        echo '<div class="stripe-integration-field">';
        
        switch ($field_type) {
            case 'text':
            case 'url':
            case 'email':
                echo '<input type="' . esc_attr($field_type) . '" ';
                echo 'id="' . esc_attr($field_id) . '" ';
                echo 'name="' . esc_attr($field_name_attr) . '" ';
                echo 'value="' . esc_attr($value) . '" ';
                echo 'class="regular-text" />';
                break;
                
            case 'password':
                echo '<input type="password" ';
                echo 'id="' . esc_attr($field_id) . '" ';
                echo 'name="' . esc_attr($field_name_attr) . '" ';
                echo 'value="' . esc_attr($value) . '" ';
                echo 'class="regular-text" />';
                break;
                
            case 'select':
                echo '<select id="' . esc_attr($field_id) . '" name="' . esc_attr($field_name_attr) . '">';
                foreach ($field_config['options'] as $option_value => $option_label) {
                    echo '<option value="' . esc_attr($option_value) . '"';
                    selected($value, $option_value);
                    echo '>' . esc_html($option_label) . '</option>';
                }
                echo '</select>';
                break;
                
            case 'checkbox':
                echo '<input type="checkbox" ';
                echo 'id="' . esc_attr($field_id) . '" ';
                echo 'name="' . esc_attr($field_name_attr) . '" ';
                echo 'value="1" ';
                checked($value, true);
                echo '/>';
                echo '<label for="' . esc_attr($field_id) . '">' . esc_html($field_config['label'] ?? '') . '</label>';
                break;
                
            case 'textarea':
                echo '<textarea ';
                echo 'id="' . esc_attr($field_id) . '" ';
                echo 'name="' . esc_attr($field_name_attr) . '" ';
                echo 'rows="5" cols="50" class="large-text">';
                echo esc_textarea($value);
                echo '</textarea>';
                break;
        }
        
        if (!empty($field_config['description'])) {
            echo '<p class="description">' . esc_html($field_config['description']) . '</p>';
        }
        
        echo '</div>';
    }
    
    /**
     * Validate settings
     */
    public function validate_settings($input) {
        $output = array();
        
        // Define validation rules
        $validation_rules = array(
            'api_base_url' => array('type' => 'url', 'required' => true),
            'api_key' => array('type' => 'text', 'required' => true),
            'vendor_id' => array('type' => 'text', 'required' => true),
            'niche' => array('type' => 'select', 'options' => array('grocery', 'catering', 'restaurant', 'retail', 'other')),
            'test_mode' => array('type' => 'boolean')
        );
        
        foreach ($validation_rules as $field => $rules) {
            if (isset($input[$field])) {
                $value = $input[$field];
                
                switch ($rules['type']) {
                    case 'url':
                        $output[$field] = esc_url_raw($value);
                        break;
                        
                    case 'text':
                        $output[$field] = sanitize_text_field($value);
                        break;
                        
                    case 'select':
                        if (in_array($value, $rules['options'])) {
                            $output[$field] = $value;
                        }
                        break;
                        
                    case 'boolean':
                        $output[$field] = (bool) $value;
                        break;
                }
                
                // Check required fields
                if (!empty($rules['required']) && empty($output[$field])) {
                    add_settings_error(
                        'stripe_integration_options',
                        $field . '_required',
                        sprintf(__('%s is required.', 'stripe-integration'), ucfirst(str_replace('_', ' ', $field)))
                    );
                }
            }
        }
        
        return $output;
    }
    
    /**
     * Add admin notices
     */
    public function add_admin_notice($message, $type = 'success') {
        add_action('admin_notices', function() use ($message, $type) {
            echo '<div class="notice notice-' . esc_attr($type) . ' is-dismissible">';
            echo '<p>' . esc_html($message) . '</p>';
            echo '</div>';
        });
    }
}
