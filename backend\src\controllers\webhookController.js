const express = require('express');
const { stripe, verifyWebhookSignature } = require('../config/stripe');
const { asyncHandler, ValidationError } = require('../middleware/errorHandler');
const { supabase } = require('../config/database');

const router = express.Router();

/**
 * Handle Stripe webhooks
 * POST /api/webhooks/stripe
 */
router.post('/stripe',
  asyncHandler(async (req, res) => {
    const signature = req.headers['stripe-signature'];
    
    if (!signature) {
      throw new ValidationError('Missing Stripe signature header');
    }

    let event;
    try {
      // Verify webhook signature
      event = verifyWebhookSignature(req.body, signature);
    } catch (err) {
      console.error('Webhook signature verification failed:', err.message);
      throw new ValidationError('Invalid webhook signature');
    }

    console.log(`Received Stripe webhook: ${event.type}`);

    // Handle the event
    try {
      switch (event.type) {
        case 'payment_intent.succeeded':
          await handlePaymentSucceeded(event.data.object);
          break;
          
        case 'payment_intent.payment_failed':
          await handlePaymentFailed(event.data.object);
          break;
          
        case 'payment_intent.canceled':
          await handlePaymentCanceled(event.data.object);
          break;
          
        case 'transfer.created':
          await handleTransferCreated(event.data.object);
          break;
          
        case 'transfer.paid':
          await handleTransferPaid(event.data.object);
          break;
          
        case 'transfer.failed':
          await handleTransferFailed(event.data.object);
          break;
          
        case 'account.updated':
          await handleAccountUpdated(event.data.object);
          break;
          
        case 'account.application.deauthorized':
          await handleAccountDeauthorized(event.data.object);
          break;
          
        default:
          console.log(`Unhandled event type: ${event.type}`);
      }
    } catch (error) {
      console.error(`Error handling webhook ${event.type}:`, error);
      // Don't throw error to avoid webhook retries for application errors
    }

    // Return success response to Stripe
    res.json({ received: true });
  })
);

/**
 * Handle successful payment
 */
async function handlePaymentSucceeded(paymentIntent) {
  console.log('Processing successful payment:', paymentIntent.id);
  
  try {
    // Update transaction status
    const { error: updateError } = await supabase
      .from('transactions')
      .update({
        status: 'succeeded',
        stripe_payment_intent_data: paymentIntent,
        completed_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('payment_intent_id', paymentIntent.id);

    if (updateError) {
      console.error('Failed to update transaction:', updateError);
      return;
    }

    // Get transaction details for commission processing
    const { data: transaction, error: fetchError } = await supabase
      .from('transactions')
      .select(`
        *,
        vendors (
          id,
          name,
          stripe_account_id,
          email
        )
      `)
      .eq('payment_intent_id', paymentIntent.id)
      .single();

    if (fetchError || !transaction) {
      console.error('Transaction not found:', paymentIntent.id);
      return;
    }

    // If vendor has Stripe Connect account and transfer wasn't automatic, create transfer
    if (transaction.vendors.stripe_account_id && !paymentIntent.transfer_data) {
      await createVendorTransfer(transaction, paymentIntent);
    }

    // Log commission earned
    await logCommissionEarned(transaction);

    console.log('Payment processing completed for:', paymentIntent.id);
  } catch (error) {
    console.error('Error processing successful payment:', error);
  }
}

/**
 * Handle failed payment
 */
async function handlePaymentFailed(paymentIntent) {
  console.log('Processing failed payment:', paymentIntent.id);
  
  try {
    const { error } = await supabase
      .from('transactions')
      .update({
        status: 'failed',
        failure_reason: paymentIntent.last_payment_error?.message || 'Payment failed',
        stripe_payment_intent_data: paymentIntent,
        updated_at: new Date().toISOString()
      })
      .eq('payment_intent_id', paymentIntent.id);

    if (error) {
      console.error('Failed to update failed payment:', error);
    }
  } catch (error) {
    console.error('Error processing failed payment:', error);
  }
}

/**
 * Handle canceled payment
 */
async function handlePaymentCanceled(paymentIntent) {
  console.log('Processing canceled payment:', paymentIntent.id);
  
  try {
    const { error } = await supabase
      .from('transactions')
      .update({
        status: 'canceled',
        cancellation_reason: paymentIntent.cancellation_reason || 'Payment canceled',
        stripe_payment_intent_data: paymentIntent,
        updated_at: new Date().toISOString()
      })
      .eq('payment_intent_id', paymentIntent.id);

    if (error) {
      console.error('Failed to update canceled payment:', error);
    }
  } catch (error) {
    console.error('Error processing canceled payment:', error);
  }
}

/**
 * Handle transfer created
 */
async function handleTransferCreated(transfer) {
  console.log('Processing transfer created:', transfer.id);
  
  try {
    // Log transfer in database
    const { error } = await supabase
      .from('transfers')
      .insert({
        stripe_transfer_id: transfer.id,
        destination_account: transfer.destination,
        amount: transfer.amount / 100, // Convert from cents
        currency: transfer.currency,
        status: 'created',
        metadata: transfer.metadata,
        created_at: new Date().toISOString()
      });

    if (error) {
      console.error('Failed to log transfer:', error);
    }
  } catch (error) {
    console.error('Error processing transfer created:', error);
  }
}

/**
 * Handle transfer paid
 */
async function handleTransferPaid(transfer) {
  console.log('Processing transfer paid:', transfer.id);
  
  try {
    const { error } = await supabase
      .from('transfers')
      .update({
        status: 'paid',
        paid_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('stripe_transfer_id', transfer.id);

    if (error) {
      console.error('Failed to update transfer status:', error);
    }
  } catch (error) {
    console.error('Error processing transfer paid:', error);
  }
}

/**
 * Handle transfer failed
 */
async function handleTransferFailed(transfer) {
  console.log('Processing transfer failed:', transfer.id);
  
  try {
    const { error } = await supabase
      .from('transfers')
      .update({
        status: 'failed',
        failure_reason: transfer.failure_message || 'Transfer failed',
        updated_at: new Date().toISOString()
      })
      .eq('stripe_transfer_id', transfer.id);

    if (error) {
      console.error('Failed to update failed transfer:', error);
    }
  } catch (error) {
    console.error('Error processing transfer failed:', error);
  }
}

/**
 * Handle account updated
 */
async function handleAccountUpdated(account) {
  console.log('Processing account updated:', account.id);
  
  try {
    const { error } = await supabase
      .from('vendors')
      .update({
        stripe_account_data: account,
        charges_enabled: account.charges_enabled,
        payouts_enabled: account.payouts_enabled,
        updated_at: new Date().toISOString()
      })
      .eq('stripe_account_id', account.id);

    if (error) {
      console.error('Failed to update vendor account:', error);
    }
  } catch (error) {
    console.error('Error processing account updated:', error);
  }
}

/**
 * Handle account deauthorized
 */
async function handleAccountDeauthorized(account) {
  console.log('Processing account deauthorized:', account.id);
  
  try {
    const { error } = await supabase
      .from('vendors')
      .update({
        stripe_account_id: null,
        charges_enabled: false,
        payouts_enabled: false,
        is_active: false,
        updated_at: new Date().toISOString()
      })
      .eq('stripe_account_id', account.id);

    if (error) {
      console.error('Failed to deauthorize vendor account:', error);
    }
  } catch (error) {
    console.error('Error processing account deauthorized:', error);
  }
}

/**
 * Create transfer to vendor
 */
async function createVendorTransfer(transaction, paymentIntent) {
  try {
    const transfer = await stripe.transfers.create({
      amount: Math.round(transaction.final_amount * 100), // Convert to cents
      currency: transaction.currency,
      destination: transaction.vendors.stripe_account_id,
      metadata: {
        transaction_id: transaction.id,
        order_id: transaction.order_id,
        vendor_id: transaction.vendor_id
      }
    });

    console.log('Transfer created:', transfer.id);
  } catch (error) {
    console.error('Failed to create vendor transfer:', error);
  }
}

/**
 * Log commission earned
 */
async function logCommissionEarned(transaction) {
  try {
    const { error } = await supabase
      .from('commission_earnings')
      .insert({
        transaction_id: transaction.id,
        vendor_id: transaction.vendor_id,
        niche: transaction.niche,
        commission_rate: transaction.commission_rate,
        commission_amount: transaction.commission_amount,
        transaction_amount: transaction.amount,
        earned_at: new Date().toISOString()
      });

    if (error) {
      console.error('Failed to log commission:', error);
    }
  } catch (error) {
    console.error('Error logging commission:', error);
  }
}

module.exports = router;
