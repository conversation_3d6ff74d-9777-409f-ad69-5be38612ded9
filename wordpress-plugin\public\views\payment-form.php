<?php
/**
 * Stripe Payment Form Template
 */

if (!defined('ABSPATH')) {
    exit;
}

$form_id = 'stripe-payment-form-' . uniqid();
$amount = $atts['amount'] ?? '';
$description = $atts['description'] ?? '';
$show_amount_field = ($atts['show_amount_field'] ?? 'false') === 'true';
?>

<div class="stripe-integration-payment-form-container">
    <form id="<?php echo esc_attr($form_id); ?>" class="stripe-integration-payment-form">
        
        <?php if (!empty($description)): ?>
            <div class="payment-description">
                <p><?php echo esc_html($description); ?></p>
            </div>
        <?php endif; ?>
        
        <div class="payment-form-fields">
            
            <?php if ($show_amount_field): ?>
                <div class="form-field">
                    <label for="<?php echo esc_attr($form_id); ?>_amount">
                        <?php _e('Amount', 'stripe-integration'); ?> *
                    </label>
                    <div class="amount-input-wrapper">
                        <span class="currency-symbol">$</span>
                        <input type="number" 
                               id="<?php echo esc_attr($form_id); ?>_amount"
                               name="amount" 
                               step="0.01" 
                               min="1" 
                               value="<?php echo esc_attr($amount); ?>"
                               placeholder="0.00" 
                               required />
                    </div>
                </div>
            <?php else: ?>
                <input type="hidden" name="amount" value="<?php echo esc_attr($amount); ?>" />
                <?php if (!empty($amount)): ?>
                    <div class="payment-amount-display">
                        <strong><?php _e('Amount:', 'stripe-integration'); ?> $<?php echo esc_html(number_format($amount, 2)); ?></strong>
                    </div>
                <?php endif; ?>
            <?php endif; ?>
            
            <div class="form-field">
                <label for="<?php echo esc_attr($form_id); ?>_customer_name">
                    <?php _e('Full Name', 'stripe-integration'); ?> *
                </label>
                <input type="text" 
                       id="<?php echo esc_attr($form_id); ?>_customer_name"
                       name="customer_name" 
                       placeholder="<?php _e('Enter your full name', 'stripe-integration'); ?>"
                       required />
            </div>
            
            <div class="form-field">
                <label for="<?php echo esc_attr($form_id); ?>_customer_email">
                    <?php _e('Email Address', 'stripe-integration'); ?> *
                </label>
                <input type="email" 
                       id="<?php echo esc_attr($form_id); ?>_customer_email"
                       name="customer_email" 
                       placeholder="<?php _e('Enter your email address', 'stripe-integration'); ?>"
                       required />
            </div>
            
            <div class="form-field">
                <label for="<?php echo esc_attr($form_id); ?>_order_id">
                    <?php _e('Order/Reference ID', 'stripe-integration'); ?>
                </label>
                <input type="text" 
                       id="<?php echo esc_attr($form_id); ?>_order_id"
                       name="order_id" 
                       placeholder="<?php _e('Optional order reference', 'stripe-integration'); ?>" />
            </div>
            
            <!-- Stripe Elements will be inserted here -->
            <div class="form-field">
                <label for="card-element">
                    <?php _e('Credit or Debit Card', 'stripe-integration'); ?> *
                </label>
                <div id="<?php echo esc_attr($form_id); ?>_card_element" class="stripe-card-element">
                    <!-- Stripe Elements will create form elements here -->
                </div>
                <div id="<?php echo esc_attr($form_id); ?>_card_errors" class="stripe-card-errors" role="alert"></div>
            </div>
            
        </div>
        
        <div class="payment-form-actions">
            <button type="submit" 
                    id="<?php echo esc_attr($form_id); ?>_submit"
                    class="stripe-payment-submit-btn">
                <span class="btn-text"><?php _e('Pay Now', 'stripe-integration'); ?></span>
                <span class="btn-spinner" style="display: none;">
                    <span class="spinner"></span>
                    <?php _e('Processing...', 'stripe-integration'); ?>
                </span>
            </button>
        </div>
        
        <div id="<?php echo esc_attr($form_id); ?>_messages" class="stripe-payment-messages"></div>
        
        <!-- Hidden fields -->
        <input type="hidden" name="form_id" value="<?php echo esc_attr($form_id); ?>" />
        <input type="hidden" name="action" value="stripe_create_payment_intent" />
        <input type="hidden" name="nonce" value="<?php echo wp_create_nonce('stripe_integration_nonce'); ?>" />
        
    </form>
</div>

<script>
jQuery(document).ready(function($) {
    // Initialize Stripe payment form
    if (typeof Stripe !== 'undefined') {
        window.StripeIntegration.initPaymentForm('<?php echo esc_js($form_id); ?>');
    } else {
        console.error('Stripe.js not loaded');
        $('#<?php echo esc_js($form_id); ?>_messages').html(
            '<div class="stripe-error"><?php _e('Payment system not available. Please try again later.', 'stripe-integration'); ?></div>'
        );
    }
});
</script>
