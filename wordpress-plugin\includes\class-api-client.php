<?php
/**
 * API Client for communicating with the backend
 */

if (!defined('ABSPATH')) {
    exit;
}

class Stripe_Integration_API_Client {
    
    /**
     * Plugin options
     */
    private $options;
    
    /**
     * API base URL
     */
    private $api_base_url;
    
    /**
     * API key
     */
    private $api_key;
    
    /**
     * Constructor
     */
    public function __construct($options) {
        $this->options = $options;
        $this->api_base_url = rtrim($options['api_base_url'] ?? '', '/');
        $this->api_key = $options['api_key'] ?? '';
    }
    
    /**
     * Make API request
     */
    private function make_request($endpoint, $method = 'GET', $data = null) {
        $url = $this->api_base_url . $endpoint;
        
        $args = array(
            'method' => $method,
            'headers' => array(
                'Content-Type' => 'application/json',
                'X-API-Key' => $this->api_key,
                'User-Agent' => 'WordPress-Stripe-Integration/' . STRIPE_INTEGRATION_VERSION
            ),
            'timeout' => 30,
            'sslverify' => !($this->options['test_mode'] ?? false)
        );
        
        if ($data && in_array($method, array('POST', 'PUT', 'PATCH'))) {
            $args['body'] = json_encode($data);
        }
        
        $response = wp_remote_request($url, $args);
        
        if (is_wp_error($response)) {
            return array(
                'success' => false,
                'message' => $response->get_error_message(),
                'data' => null
            );
        }
        
        $status_code = wp_remote_retrieve_response_code($response);
        $body = wp_remote_retrieve_body($response);
        $decoded_body = json_decode($body, true);
        
        if ($status_code >= 200 && $status_code < 300) {
            return array(
                'success' => true,
                'message' => $decoded_body['message'] ?? 'Success',
                'data' => $decoded_body['data'] ?? $decoded_body
            );
        } else {
            return array(
                'success' => false,
                'message' => $decoded_body['message'] ?? 'API request failed',
                'data' => $decoded_body
            );
        }
    }
    
    /**
     * Create payment intent
     */
    public function create_payment_intent($amount, $order_id, $customer_email, $customer_name) {
        $data = array(
            'amount' => $amount,
            'order_id' => $order_id,
            'customer_email' => $customer_email,
            'customer_name' => $customer_name,
            'vendor_id' => $this->options['vendor_id'] ?? '',
            'niche' => $this->options['niche'] ?? 'other',
            'currency' => get_option('woocommerce_currency', 'USD'),
            'metadata' => array(
                'wordpress_site' => get_site_url(),
                'plugin_version' => STRIPE_INTEGRATION_VERSION
            )
        );
        
        return $this->make_request('/api/payments/create-intent', 'POST', $data);
    }
    
    /**
     * Confirm payment
     */
    public function confirm_payment($payment_intent_id) {
        $data = array(
            'payment_intent_id' => $payment_intent_id
        );
        
        return $this->make_request('/api/payments/confirm', 'POST', $data);
    }
    
    /**
     * Cancel payment
     */
    public function cancel_payment($payment_intent_id, $reason = '') {
        $data = array(
            'payment_intent_id' => $payment_intent_id,
            'reason' => $reason
        );
        
        return $this->make_request('/api/payments/cancel', 'POST', $data);
    }
    
    /**
     * Get transaction details
     */
    public function get_transaction($payment_intent_id) {
        return $this->make_request('/api/payments/transaction/' . urlencode($payment_intent_id));
    }
    
    /**
     * Get transaction history
     */
    public function get_transaction_history($filters = array()) {
        $query_string = http_build_query($filters);
        $endpoint = '/api/payments/transactions';
        if ($query_string) {
            $endpoint .= '?' . $query_string;
        }
        
        return $this->make_request($endpoint);
    }
    
    /**
     * Get commission rates
     */
    public function get_commission_rates($niche = null) {
        $endpoint = '/api/commissions/rates';
        if ($niche) {
            $endpoint .= '?niche=' . urlencode($niche);
        }
        
        return $this->make_request($endpoint);
    }
    
    /**
     * Calculate commission
     */
    public function calculate_commission($amount, $niche = null) {
        $data = array(
            'amount' => $amount,
            'niche' => $niche ?: ($this->options['niche'] ?? 'other'),
            'vendor_id' => $this->options['vendor_id'] ?? ''
        );
        
        return $this->make_request('/api/commissions/calculate', 'POST', $data);
    }
    
    /**
     * Get vendor information
     */
    public function get_vendor_info($vendor_id = null) {
        $vendor_id = $vendor_id ?: ($this->options['vendor_id'] ?? '');
        if (empty($vendor_id)) {
            return array(
                'success' => false,
                'message' => 'No vendor ID configured',
                'data' => null
            );
        }
        
        return $this->make_request('/api/vendors/' . urlencode($vendor_id));
    }
    
    /**
     * Get vendor Stripe account status
     */
    public function get_vendor_stripe_status($vendor_id = null) {
        $vendor_id = $vendor_id ?: ($this->options['vendor_id'] ?? '');
        if (empty($vendor_id)) {
            return array(
                'success' => false,
                'message' => 'No vendor ID configured',
                'data' => null
            );
        }
        
        return $this->make_request('/api/vendors/' . urlencode($vendor_id) . '/stripe-status');
    }
    
    /**
     * Test API connection
     */
    public function test_connection() {
        return $this->make_request('/api/health');
    }
    
    /**
     * Validate API key
     */
    public function validate_api_key() {
        return $this->make_request('/api/auth/validate');
    }
    
    /**
     * Get API statistics
     */
    public function get_statistics($period = '30d') {
        $data = array(
            'period' => $period,
            'vendor_id' => $this->options['vendor_id'] ?? ''
        );
        
        return $this->make_request('/api/commissions/statistics', 'POST', $data);
    }
    
    /**
     * Log error for debugging
     */
    private function log_error($message, $context = array()) {
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('Stripe Integration API Error: ' . $message . ' Context: ' . json_encode($context));
        }
    }
    
    /**
     * Check if API is configured
     */
    public function is_configured() {
        return !empty($this->api_base_url) && !empty($this->api_key);
    }
    
    /**
     * Get configuration status
     */
    public function get_config_status() {
        $status = array(
            'api_base_url' => !empty($this->api_base_url),
            'api_key' => !empty($this->api_key),
            'vendor_id' => !empty($this->options['vendor_id'] ?? ''),
            'niche' => !empty($this->options['niche'] ?? '')
        );
        
        $status['is_complete'] = array_reduce($status, function($carry, $item) {
            return $carry && $item;
        }, true);
        
        return $status;
    }
}
