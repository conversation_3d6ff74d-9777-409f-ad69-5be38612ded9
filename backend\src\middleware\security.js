/**
 * Enhanced Security Middleware
 */

const rateLimit = require('express-rate-limit');
const slowDown = require('express-slow-down');
const { body, validationResult } = require('express-validator');
const crypto = require('crypto');
const validator = require('validator');
const logger = require('../utils/logger');
const securityConfig = require('../../config/security');

/**
 * Enhanced rate limiting for different endpoints
 */
const createRateLimit = (windowMs, max, message, skipSuccessfulRequests = true) => {
    return rateLimit({
        windowMs,
        max,
        message: { error: message },
        standardHeaders: true,
        legacyHeaders: false,
        skipSuccessfulRequests,
        handler: (req, res) => {
            logger.warn('Rate limit exceeded', {
                ip: req.ip,
                userAgent: req.get('User-Agent'),
                endpoint: req.path,
                method: req.method
            });
            res.status(429).json({ error: message });
        }
    });
};

/**
 * Rate limiting configurations
 */
const rateLimits = {
    // General API rate limit
    general: createRateLimit(15 * 60 * 1000, 100, 'Too many requests, please try again later'),
    
    // Payment endpoints - more restrictive
    payment: createRateLimit(15 * 60 * 1000, 20, 'Too many payment requests, please try again later'),
    
    // Authentication endpoints - very restrictive
    auth: createRateLimit(15 * 60 * 1000, 5, 'Too many authentication attempts, please try again later', false),
    
    // Webhook endpoints - moderate
    webhook: createRateLimit(1 * 60 * 1000, 50, 'Too many webhook requests'),
    
    // Commission endpoints
    commission: createRateLimit(15 * 60 * 1000, 30, 'Too many commission requests, please try again later')
};

/**
 * Speed limiting (progressive delay)
 */
const speedLimiter = slowDown({
    windowMs: 15 * 60 * 1000, // 15 minutes
    delayAfter: 10, // Allow 10 requests per windowMs without delay
    delayMs: 500, // Add 500ms delay per request after delayAfter
    maxDelayMs: 20000, // Maximum delay of 20 seconds
    skipSuccessfulRequests: true
});

/**
 * Request signature validation
 */
const validateRequestSignature = (secret) => {
    return (req, res, next) => {
        const signature = req.get('X-Request-Signature');
        const timestamp = req.get('X-Request-Timestamp');
        
        if (!signature || !timestamp) {
            return res.status(401).json({ error: 'Missing request signature or timestamp' });
        }
        
        // Check timestamp (prevent replay attacks)
        const now = Date.now();
        const requestTime = parseInt(timestamp);
        const timeDiff = Math.abs(now - requestTime);
        
        if (timeDiff > 5 * 60 * 1000) { // 5 minutes tolerance
            return res.status(401).json({ error: 'Request timestamp too old' });
        }
        
        // Validate signature
        const payload = timestamp + req.method + req.path + (req.body ? JSON.stringify(req.body) : '');
        const expectedSignature = crypto
            .createHmac('sha256', secret)
            .update(payload)
            .digest('hex');
        
        if (!crypto.timingSafeEqual(Buffer.from(signature, 'hex'), Buffer.from(expectedSignature, 'hex'))) {
            logger.warn('Invalid request signature', {
                ip: req.ip,
                userAgent: req.get('User-Agent'),
                endpoint: req.path,
                method: req.method
            });
            return res.status(401).json({ error: 'Invalid request signature' });
        }
        
        next();
    };
};

/**
 * Input sanitization middleware
 */
const sanitizeInput = (req, res, next) => {
    const sanitizeValue = (value) => {
        if (typeof value === 'string') {
            // Remove potential XSS patterns
            return value
                .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
                .replace(/javascript:/gi, '')
                .replace(/on\w+\s*=/gi, '')
                .trim();
        }
        return value;
    };
    
    const sanitizeObject = (obj) => {
        if (obj && typeof obj === 'object') {
            for (const key in obj) {
                if (obj.hasOwnProperty(key)) {
                    if (typeof obj[key] === 'object') {
                        sanitizeObject(obj[key]);
                    } else {
                        obj[key] = sanitizeValue(obj[key]);
                    }
                }
            }
        }
    };
    
    // Sanitize request body
    if (req.body) {
        sanitizeObject(req.body);
    }
    
    // Sanitize query parameters
    if (req.query) {
        sanitizeObject(req.query);
    }
    
    next();
};

/**
 * Security headers middleware
 */
const securityHeaders = (req, res, next) => {
    // Content Security Policy
    res.setHeader('Content-Security-Policy', 
        "default-src 'self'; " +
        "script-src 'self' 'unsafe-inline' https://js.stripe.com; " +
        "style-src 'self' 'unsafe-inline'; " +
        "img-src 'self' data: https:; " +
        "connect-src 'self' https://api.stripe.com; " +
        "frame-src https://js.stripe.com https://hooks.stripe.com;"
    );
    
    // Additional security headers
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.setHeader('X-Frame-Options', 'DENY');
    res.setHeader('X-XSS-Protection', '1; mode=block');
    res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
    res.setHeader('Permissions-Policy', 'geolocation=(), microphone=(), camera=()');
    
    // Remove server information
    res.removeHeader('X-Powered-By');
    
    next();
};

/**
 * Request logging for security monitoring
 */
const securityLogger = (req, res, next) => {
    const startTime = Date.now();
    
    // Log request details
    logger.info('API Request', {
        method: req.method,
        path: req.path,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        contentLength: req.get('Content-Length'),
        timestamp: new Date().toISOString()
    });
    
    // Log response details
    const originalSend = res.send;
    res.send = function(data) {
        const duration = Date.now() - startTime;
        
        logger.info('API Response', {
            method: req.method,
            path: req.path,
            statusCode: res.statusCode,
            duration: `${duration}ms`,
            ip: req.ip,
            timestamp: new Date().toISOString()
        });
        
        // Log security events
        if (res.statusCode === 401 || res.statusCode === 403) {
            logger.warn('Security Event - Unauthorized Access', {
                method: req.method,
                path: req.path,
                statusCode: res.statusCode,
                ip: req.ip,
                userAgent: req.get('User-Agent'),
                timestamp: new Date().toISOString()
            });
        }
        
        originalSend.call(this, data);
    };
    
    next();
};

/**
 * Validate environment variables
 */
const validateEnvironment = () => {
    const requiredVars = [
        'SUPABASE_URL',
        'SUPABASE_ANON_KEY',
        'STRIPE_SECRET_KEY',
        'JWT_SECRET',
        'API_SECRET'
    ];
    
    const missing = requiredVars.filter(varName => !process.env[varName]);
    
    if (missing.length > 0) {
        logger.error('Missing required environment variables', { missing });
        throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
    }
    
    // Validate JWT secret strength
    if (process.env.JWT_SECRET.length < 32) {
        logger.error('JWT_SECRET is too weak (minimum 32 characters required)');
        throw new Error('JWT_SECRET is too weak');
    }
    
    // Validate API secret strength
    if (process.env.API_SECRET.length < 32) {
        logger.error('API_SECRET is too weak (minimum 32 characters required)');
        throw new Error('API_SECRET is too weak');
    }
};

/**
 * IP whitelist middleware (for production)
 */
const ipWhitelist = (allowedIPs = []) => {
    return (req, res, next) => {
        if (process.env.NODE_ENV === 'production' && allowedIPs.length > 0) {
            const clientIP = req.ip || req.connection.remoteAddress;
            
            if (!allowedIPs.includes(clientIP)) {
                logger.warn('IP not whitelisted', {
                    ip: clientIP,
                    userAgent: req.get('User-Agent'),
                    endpoint: req.path
                });
                return res.status(403).json({ error: 'Access denied' });
            }
        }
        next();
    };
};

module.exports = {
    rateLimits,
    speedLimiter,
    validateRequestSignature,
    sanitizeInput,
    securityHeaders,
    securityLogger,
    validateEnvironment,
    ipWhitelist
};
