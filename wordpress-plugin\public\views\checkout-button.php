<?php
/**
 * Simple Stripe Checkout Button Template
 */

if (!defined('ABSPATH')) {
    exit;
}

$button_text = $atts['text'] ?? __('Pay with Stripe', 'stripe-integration');
$amount = $atts['amount'] ?? '';
$description = $atts['description'] ?? '';
$button_id = 'stripe-checkout-btn-' . uniqid();
?>

<div class="stripe-checkout-button-container">
    
    <?php if (!empty($description)): ?>
        <div class="checkout-description">
            <p><?php echo esc_html($description); ?></p>
        </div>
    <?php endif; ?>
    
    <?php if (!empty($amount)): ?>
        <div class="checkout-amount">
            <strong><?php _e('Amount:', 'stripe-integration'); ?> $<?php echo esc_html(number_format($amount, 2)); ?></strong>
        </div>
    <?php endif; ?>
    
    <button type="button" 
            id="<?php echo esc_attr($button_id); ?>"
            class="stripe-checkout-button"
            data-amount="<?php echo esc_attr($amount); ?>"
            data-description="<?php echo esc_attr($description); ?>">
        <?php echo esc_html($button_text); ?>
    </button>
    
    <div id="<?php echo esc_attr($button_id); ?>_messages" class="stripe-checkout-messages"></div>
    
</div>

<script>
jQuery(document).ready(function($) {
    $('#<?php echo esc_js($button_id); ?>').on('click', function() {
        var button = $(this);
        var amount = button.data('amount');
        var description = button.data('description');
        
        // Create a modal or redirect to payment form
        // For now, we'll create a simple modal with the payment form
        var modal = $('<div class="stripe-checkout-modal">' +
            '<div class="stripe-checkout-modal-content">' +
            '<span class="stripe-checkout-modal-close">&times;</span>' +
            '<h3><?php _e('Complete Your Payment', 'stripe-integration'); ?></h3>' +
            '<div class="stripe-checkout-modal-body"></div>' +
            '</div>' +
            '</div>');
        
        // Add modal styles
        modal.css({
            'position': 'fixed',
            'z-index': '9999',
            'left': '0',
            'top': '0',
            'width': '100%',
            'height': '100%',
            'background-color': 'rgba(0,0,0,0.5)',
            'display': 'flex',
            'align-items': 'center',
            'justify-content': 'center'
        });
        
        modal.find('.stripe-checkout-modal-content').css({
            'background-color': '#fff',
            'padding': '20px',
            'border-radius': '8px',
            'max-width': '500px',
            'width': '90%',
            'max-height': '90%',
            'overflow-y': 'auto',
            'position': 'relative'
        });
        
        modal.find('.stripe-checkout-modal-close').css({
            'position': 'absolute',
            'top': '10px',
            'right': '15px',
            'font-size': '24px',
            'cursor': 'pointer',
            'color': '#999'
        });
        
        // Load payment form via AJAX
        $.ajax({
            url: stripe_integration_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'stripe_get_payment_form',
                amount: amount,
                description: description,
                nonce: '<?php echo wp_create_nonce('stripe_integration_nonce'); ?>'
            },
            success: function(response) {
                if (response.success) {
                    modal.find('.stripe-checkout-modal-body').html(response.data);
                    $('body').append(modal);
                    
                    // Initialize payment form
                    if (window.StripeIntegration) {
                        // Find the form ID in the loaded content
                        var formId = modal.find('.stripe-integration-payment-form input[name="form_id"]').val();
                        if (formId) {
                            window.StripeIntegration.initPaymentForm(formId);
                        }
                    }
                } else {
                    alert('<?php _e('Unable to load payment form. Please try again.', 'stripe-integration'); ?>');
                }
            },
            error: function() {
                alert('<?php _e('Network error. Please try again.', 'stripe-integration'); ?>');
            }
        });
        
        // Close modal handlers
        modal.on('click', '.stripe-checkout-modal-close', function() {
            modal.remove();
        });
        
        modal.on('click', function(e) {
            if (e.target === modal[0]) {
                modal.remove();
            }
        });
        
        // Close modal on successful payment
        $(document).on('stripe_integration_payment_success', function() {
            modal.remove();
        });
    });
});
</script>
