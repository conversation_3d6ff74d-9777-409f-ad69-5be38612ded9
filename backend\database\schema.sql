-- Stripe Integration Database Schema
-- This file contains the SQL schema for Supabase database

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users table for WordPress site authentication
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    wordpress_site TEXT NOT NULL UNIQUE,
    email TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- API keys table for WordPress integration
CREATE TABLE IF NOT EXISTS api_keys (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    key_hash TEXT NOT NULL UNIQUE,
    wordpress_site TEXT NOT NULL,
    permissions TEXT[] DEFAULT ARRAY['payments', 'commissions'],
    description TEXT DEFAULT '',
    is_active BOOLEAN DEFAULT TRUE,
    last_used_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    revoked_at TIMESTAMP WITH TIME ZONE
);

-- Vendors table
CREATE TABLE IF NOT EXISTS vendors (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    email TEXT NOT NULL UNIQUE,
    niche TEXT NOT NULL CHECK (niche IN ('grocery', 'catering', 'restaurant', 'retail', 'other')),
    country TEXT DEFAULT 'US',
    business_type TEXT DEFAULT 'individual' CHECK (business_type IN ('individual', 'company')),
    stripe_account_id TEXT UNIQUE,
    stripe_account_data JSONB,
    charges_enabled BOOLEAN DEFAULT FALSE,
    payouts_enabled BOOLEAN DEFAULT FALSE,
    commission_adjustment DECIMAL(5,4) DEFAULT 0,
    tier TEXT DEFAULT 'bronze' CHECK (tier IN ('bronze', 'silver', 'gold', 'platinum')),
    metadata JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Commission rates table
CREATE TABLE IF NOT EXISTS commission_rates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    niche TEXT NOT NULL CHECK (niche IN ('grocery', 'catering', 'restaurant', 'retail', 'other', 'default')),
    rate DECIMAL(5,4) NOT NULL CHECK (rate >= 0.001 AND rate <= 0.5),
    min_amount DECIMAL(10,2) DEFAULT 0,
    max_amount DECIMAL(10,2),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Transactions table
CREATE TABLE IF NOT EXISTS transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    payment_intent_id TEXT NOT NULL UNIQUE,
    vendor_id UUID NOT NULL REFERENCES vendors(id),
    order_id TEXT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    commission_rate DECIMAL(5,4) NOT NULL,
    commission_amount DECIMAL(10,2) NOT NULL,
    final_amount DECIMAL(10,2) NOT NULL,
    currency TEXT DEFAULT 'usd',
    niche TEXT NOT NULL,
    customer_email TEXT NOT NULL,
    customer_name TEXT NOT NULL,
    payment_method_id TEXT,
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'succeeded', 'failed', 'canceled')),
    failure_reason TEXT,
    cancellation_reason TEXT,
    stripe_payment_intent_data JSONB,
    metadata JSONB DEFAULT '{}',
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Transfers table for tracking Stripe transfers to vendors
CREATE TABLE IF NOT EXISTS transfers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    stripe_transfer_id TEXT NOT NULL UNIQUE,
    destination_account TEXT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    currency TEXT DEFAULT 'usd',
    status TEXT NOT NULL DEFAULT 'created' CHECK (status IN ('created', 'paid', 'failed')),
    failure_reason TEXT,
    metadata JSONB DEFAULT '{}',
    paid_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Commission earnings table for tracking commission history
CREATE TABLE IF NOT EXISTS commission_earnings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    transaction_id UUID NOT NULL REFERENCES transactions(id),
    vendor_id UUID NOT NULL REFERENCES vendors(id),
    niche TEXT NOT NULL,
    commission_rate DECIMAL(5,4) NOT NULL,
    commission_amount DECIMAL(10,2) NOT NULL,
    transaction_amount DECIMAL(10,2) NOT NULL,
    earned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for better performance
CREATE INDEX IF NOT EXISTS idx_api_keys_user_id ON api_keys(user_id);
CREATE INDEX IF NOT EXISTS idx_api_keys_key_hash ON api_keys(key_hash);
CREATE INDEX IF NOT EXISTS idx_api_keys_is_active ON api_keys(is_active);

CREATE INDEX IF NOT EXISTS idx_vendors_email ON vendors(email);
CREATE INDEX IF NOT EXISTS idx_vendors_niche ON vendors(niche);
CREATE INDEX IF NOT EXISTS idx_vendors_stripe_account_id ON vendors(stripe_account_id);
CREATE INDEX IF NOT EXISTS idx_vendors_is_active ON vendors(is_active);

CREATE INDEX IF NOT EXISTS idx_commission_rates_niche ON commission_rates(niche);
CREATE INDEX IF NOT EXISTS idx_commission_rates_is_active ON commission_rates(is_active);

CREATE INDEX IF NOT EXISTS idx_transactions_payment_intent_id ON transactions(payment_intent_id);
CREATE INDEX IF NOT EXISTS idx_transactions_vendor_id ON transactions(vendor_id);
CREATE INDEX IF NOT EXISTS idx_transactions_status ON transactions(status);
CREATE INDEX IF NOT EXISTS idx_transactions_created_at ON transactions(created_at);
CREATE INDEX IF NOT EXISTS idx_transactions_niche ON transactions(niche);

CREATE INDEX IF NOT EXISTS idx_transfers_stripe_transfer_id ON transfers(stripe_transfer_id);
CREATE INDEX IF NOT EXISTS idx_transfers_status ON transfers(status);

CREATE INDEX IF NOT EXISTS idx_commission_earnings_transaction_id ON commission_earnings(transaction_id);
CREATE INDEX IF NOT EXISTS idx_commission_earnings_vendor_id ON commission_earnings(vendor_id);
CREATE INDEX IF NOT EXISTS idx_commission_earnings_earned_at ON commission_earnings(earned_at);

-- Insert default commission rates
INSERT INTO commission_rates (niche, rate, min_amount, max_amount, is_active) VALUES
('default', 0.05, 0, NULL, TRUE),
('grocery', 0.03, 0, NULL, TRUE),
('catering', 0.06, 0, NULL, TRUE),
('restaurant', 0.04, 0, NULL, TRUE),
('retail', 0.05, 0, NULL, TRUE),
('other', 0.05, 0, NULL, TRUE)
ON CONFLICT DO NOTHING;

-- Row Level Security (RLS) policies
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE api_keys ENABLE ROW LEVEL SECURITY;
ALTER TABLE vendors ENABLE ROW LEVEL SECURITY;
ALTER TABLE commission_rates ENABLE ROW LEVEL SECURITY;
ALTER TABLE transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE transfers ENABLE ROW LEVEL SECURITY;
ALTER TABLE commission_earnings ENABLE ROW LEVEL SECURITY;

-- Create policies (these can be customized based on your security requirements)
-- For now, we'll allow service role to access everything
CREATE POLICY "Service role can access all users" ON users FOR ALL USING (auth.role() = 'service_role');
CREATE POLICY "Service role can access all api_keys" ON api_keys FOR ALL USING (auth.role() = 'service_role');
CREATE POLICY "Service role can access all vendors" ON vendors FOR ALL USING (auth.role() = 'service_role');
CREATE POLICY "Service role can access all commission_rates" ON commission_rates FOR ALL USING (auth.role() = 'service_role');
CREATE POLICY "Service role can access all transactions" ON transactions FOR ALL USING (auth.role() = 'service_role');
CREATE POLICY "Service role can access all transfers" ON transfers FOR ALL USING (auth.role() = 'service_role');
CREATE POLICY "Service role can access all commission_earnings" ON commission_earnings FOR ALL USING (auth.role() = 'service_role');
