const express = require('express');
const { validate, schemas } = require('../middleware/validation');
const { asyncHandler, ValidationError, NotFoundError } = require('../middleware/errorHandler');
const { requirePermissions } = require('../middleware/auth');
const { supabase } = require('../config/database');
const commissionService = require('../services/commissionService');

const router = express.Router();

/**
 * Get commission rates
 * GET /api/commissions/rates
 */
router.get('/rates',
  asyncHandler(async (req, res) => {
    const { niche, is_active = true } = req.query;

    let query = supabase
      .from('commission_rates')
      .select('*')
      .order('niche', { ascending: true });

    if (niche) {
      query = query.eq('niche', niche);
    }

    if (is_active !== undefined) {
      query = query.eq('is_active', is_active === 'true');
    }

    const { data: rates, error } = await query;

    if (error) {
      throw error;
    }

    res.json({
      success: true,
      data: rates
    });
  })
);

/**
 * Create commission rate
 * POST /api/commissions/rates
 */
router.post('/rates',
  requirePermissions(['commissions', 'admin']),
  validate(schemas.createCommissionRate),
  asyncHandler(async (req, res) => {
    const { niche, rate, min_amount, max_amount, is_active } = req.body;

    // Check if commission rate already exists for this niche
    const { data: existingRate, error: checkError } = await supabase
      .from('commission_rates')
      .select('*')
      .eq('niche', niche)
      .eq('is_active', true)
      .single();

    if (checkError && checkError.code !== 'PGRST116') {
      throw checkError;
    }

    if (existingRate) {
      throw new ValidationError(`Active commission rate already exists for niche: ${niche}`);
    }

    // Validate rate bounds
    const minRate = parseFloat(process.env.MIN_COMMISSION_RATE) || 0.01;
    const maxRate = parseFloat(process.env.MAX_COMMISSION_RATE) || 0.15;

    if (rate < minRate || rate > maxRate) {
      throw new ValidationError(`Commission rate must be between ${minRate * 100}% and ${maxRate * 100}%`);
    }

    // Create commission rate
    const { data: newRate, error } = await supabase
      .from('commission_rates')
      .insert({
        niche,
        rate,
        min_amount: min_amount || 0,
        max_amount,
        is_active: is_active !== false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      throw error;
    }

    res.status(201).json({
      success: true,
      message: 'Commission rate created successfully',
      data: newRate
    });
  })
);

/**
 * Update commission rate
 * PUT /api/commissions/rates/:id
 */
router.put('/rates/:id',
  requirePermissions(['commissions', 'admin']),
  validate(schemas.updateCommissionRate),
  asyncHandler(async (req, res) => {
    const { id } = req.params;
    const updateData = { ...req.body, updated_at: new Date().toISOString() };

    // Validate rate if provided
    if (updateData.rate) {
      const minRate = parseFloat(process.env.MIN_COMMISSION_RATE) || 0.01;
      const maxRate = parseFloat(process.env.MAX_COMMISSION_RATE) || 0.15;

      if (updateData.rate < minRate || updateData.rate > maxRate) {
        throw new ValidationError(`Commission rate must be between ${minRate * 100}% and ${maxRate * 100}%`);
      }
    }

    const { data: updatedRate, error } = await supabase
      .from('commission_rates')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        throw new NotFoundError('Commission rate not found');
      }
      throw error;
    }

    res.json({
      success: true,
      message: 'Commission rate updated successfully',
      data: updatedRate
    });
  })
);

/**
 * Delete commission rate
 * DELETE /api/commissions/rates/:id
 */
router.delete('/rates/:id',
  requirePermissions(['commissions', 'admin']),
  asyncHandler(async (req, res) => {
    const { id } = req.params;

    const { error } = await supabase
      .from('commission_rates')
      .delete()
      .eq('id', id);

    if (error) {
      throw error;
    }

    res.json({
      success: true,
      message: 'Commission rate deleted successfully'
    });
  })
);

/**
 * Calculate commission for a transaction
 * POST /api/commissions/calculate
 */
router.post('/calculate',
  asyncHandler(async (req, res) => {
    const { amount, niche, vendor_id } = req.body;

    if (!amount || !niche) {
      throw new ValidationError('Amount and niche are required');
    }

    if (amount <= 0) {
      throw new ValidationError('Amount must be positive');
    }

    const commissionData = await commissionService.calculateCommission({
      amount,
      niche,
      vendor_id
    });

    res.json({
      success: true,
      data: commissionData
    });
  })
);

/**
 * Get commission statistics
 * GET /api/commissions/stats
 */
router.get('/stats',
  requirePermissions(['commissions', 'admin']),
  asyncHandler(async (req, res) => {
    const {
      start_date,
      end_date,
      niche,
      vendor_id
    } = req.query;

    // Build base query
    let query = supabase
      .from('transactions')
      .select('commission_amount, niche, vendor_id, created_at')
      .eq('status', 'succeeded');

    // Apply filters
    if (start_date) {
      query = query.gte('created_at', start_date);
    }
    if (end_date) {
      query = query.lte('created_at', end_date);
    }
    if (niche) {
      query = query.eq('niche', niche);
    }
    if (vendor_id) {
      query = query.eq('vendor_id', vendor_id);
    }

    const { data: transactions, error } = await query;

    if (error) {
      throw error;
    }

    // Calculate statistics
    const stats = {
      total_commission: 0,
      transaction_count: transactions.length,
      average_commission: 0,
      by_niche: {},
      by_month: {}
    };

    transactions.forEach(transaction => {
      const commission = parseFloat(transaction.commission_amount) || 0;
      stats.total_commission += commission;

      // Group by niche
      if (!stats.by_niche[transaction.niche]) {
        stats.by_niche[transaction.niche] = {
          total_commission: 0,
          transaction_count: 0
        };
      }
      stats.by_niche[transaction.niche].total_commission += commission;
      stats.by_niche[transaction.niche].transaction_count += 1;

      // Group by month
      const month = new Date(transaction.created_at).toISOString().substring(0, 7);
      if (!stats.by_month[month]) {
        stats.by_month[month] = {
          total_commission: 0,
          transaction_count: 0
        };
      }
      stats.by_month[month].total_commission += commission;
      stats.by_month[month].transaction_count += 1;
    });

    stats.average_commission = stats.transaction_count > 0 
      ? stats.total_commission / stats.transaction_count 
      : 0;

    res.json({
      success: true,
      data: stats
    });
  })
);

/**
 * Get commission history for a vendor
 * GET /api/commissions/vendor/:vendor_id/history
 */
router.get('/vendor/:vendor_id/history',
  asyncHandler(async (req, res) => {
    const { vendor_id } = req.params;
    const {
      limit = 50,
      offset = 0,
      start_date,
      end_date
    } = req.query;

    let query = supabase
      .from('transactions')
      .select('*')
      .eq('vendor_id', vendor_id)
      .eq('status', 'succeeded')
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (start_date) {
      query = query.gte('created_at', start_date);
    }
    if (end_date) {
      query = query.lte('created_at', end_date);
    }

    const { data: transactions, error } = await query;

    if (error) {
      throw error;
    }

    // Calculate totals
    const totals = transactions.reduce((acc, transaction) => {
      acc.total_amount += parseFloat(transaction.amount) || 0;
      acc.total_commission += parseFloat(transaction.commission_amount) || 0;
      acc.net_amount += parseFloat(transaction.final_amount) || 0;
      return acc;
    }, {
      total_amount: 0,
      total_commission: 0,
      net_amount: 0
    });

    res.json({
      success: true,
      data: {
        transactions,
        totals,
        pagination: {
          limit: parseInt(limit),
          offset: parseInt(offset),
          has_more: transactions.length === parseInt(limit)
        }
      }
    });
  })
);

module.exports = router;
