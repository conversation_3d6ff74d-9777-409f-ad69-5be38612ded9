/**
 * Stripe Integration Frontend Styles
 */

/* Payment Form Container */
.stripe-integration-payment-form-container {
    max-width: 500px;
    margin: 20px auto;
    padding: 20px;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Payment Form */
.stripe-integration-payment-form {
    width: 100%;
}

.payment-description {
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-left: 4px solid #007cba;
    border-radius: 4px;
}

.payment-description p {
    margin: 0;
    color: #333;
}

/* Form Fields */
.payment-form-fields {
    margin-bottom: 20px;
}

.form-field {
    margin-bottom: 20px;
}

.form-field label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #333;
}

.form-field input[type="text"],
.form-field input[type="email"],
.form-field input[type="number"] {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
    transition: border-color 0.3s ease;
    box-sizing: border-box;
}

.form-field input[type="text"]:focus,
.form-field input[type="email"]:focus,
.form-field input[type="number"]:focus {
    outline: none;
    border-color: #007cba;
    box-shadow: 0 0 0 2px rgba(0, 124, 186, 0.1);
}

.form-field input.error {
    border-color: #dc3232;
    box-shadow: 0 0 0 2px rgba(220, 50, 50, 0.1);
}

/* Amount Input */
.amount-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.currency-symbol {
    position: absolute;
    left: 12px;
    color: #666;
    font-weight: 600;
    z-index: 1;
}

.amount-input-wrapper input {
    padding-left: 30px;
}

.payment-amount-display {
    padding: 15px;
    background: #f0f8ff;
    border: 1px solid #b3d9ff;
    border-radius: 4px;
    text-align: center;
    margin-bottom: 10px;
}

/* Stripe Card Element */
.stripe-card-element {
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: #fff;
    transition: border-color 0.3s ease;
}

.stripe-card-element:focus-within {
    border-color: #007cba;
    box-shadow: 0 0 0 2px rgba(0, 124, 186, 0.1);
}

.stripe-card-errors {
    color: #dc3232;
    font-size: 14px;
    margin-top: 5px;
    min-height: 20px;
}

/* Submit Button */
.payment-form-actions {
    text-align: center;
    margin-top: 30px;
}

.stripe-payment-submit-btn {
    background: #007cba;
    color: white;
    border: none;
    padding: 15px 30px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    min-width: 150px;
    position: relative;
}

.stripe-payment-submit-btn:hover:not(:disabled) {
    background: #005a87;
}

.stripe-payment-submit-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
}

.stripe-payment-submit-btn .btn-spinner {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.stripe-payment-submit-btn .spinner {
    width: 16px;
    height: 16px;
    border: 2px solid #ffffff;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Messages */
.stripe-payment-messages {
    margin-top: 20px;
}

.stripe-error {
    padding: 12px;
    background: #ffeaea;
    border: 1px solid #dc3232;
    border-radius: 4px;
    color: #dc3232;
    font-weight: 500;
}

.stripe-success {
    padding: 12px;
    background: #eafaea;
    border: 1px solid #46b450;
    border-radius: 4px;
    color: #46b450;
    font-weight: 500;
}

/* Checkout Button (for simple checkout shortcode) */
.stripe-checkout-button-container {
    text-align: center;
    margin: 20px 0;
}

.stripe-checkout-button {
    background: #007cba;
    color: white;
    border: none;
    padding: 15px 30px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.stripe-checkout-button:hover {
    background: #005a87;
    color: white;
    text-decoration: none;
}

/* Responsive Design */
@media (max-width: 600px) {
    .stripe-integration-payment-form-container {
        margin: 10px;
        padding: 15px;
    }
    
    .form-field input[type="text"],
    .form-field input[type="email"],
    .form-field input[type="number"] {
        font-size: 16px; /* Prevent zoom on iOS */
    }
    
    .stripe-payment-submit-btn {
        width: 100%;
        padding: 15px;
    }
}

/* Admin Styles */
.stripe-integration-admin-container {
    display: flex;
    gap: 20px;
    margin-top: 20px;
}

.stripe-integration-main-content {
    flex: 2;
}

.stripe-integration-sidebar {
    flex: 1;
    max-width: 350px;
}

.stripe-integration-status-card,
.stripe-integration-help-card {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
}

.stripe-integration-status-card h3,
.stripe-integration-help-card h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #333;
}

.stripe-integration-status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.stripe-integration-status-item:last-child {
    border-bottom: none;
}

.status-label {
    font-weight: 500;
}

.status-indicator {
    font-weight: bold;
    font-size: 16px;
}

.status-indicator.success {
    color: #46b450;
}

.status-indicator.error {
    color: #dc3232;
}

.stripe-integration-status-overall {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 2px solid #eee;
    text-align: center;
}

.stripe-integration-test-buttons {
    margin-top: 20px;
}

.stripe-integration-test-buttons button {
    display: block;
    width: 100%;
    margin-bottom: 10px;
}

.stripe-integration-test-results {
    margin-top: 15px;
}

.stripe-integration-test-results .notice {
    margin: 0;
    padding: 10px;
}

/* Settings Form */
.stripe-integration-settings-section {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
}

.stripe-integration-settings-section h2 {
    margin-top: 0;
    margin-bottom: 10px;
    color: #333;
}

.stripe-integration-field {
    margin-bottom: 15px;
}

/* Help Section */
.stripe-integration-help-card ol {
    padding-left: 20px;
}

.stripe-integration-help-card li {
    margin-bottom: 8px;
    line-height: 1.5;
}

/* Responsive Admin */
@media (max-width: 900px) {
    .stripe-integration-admin-container {
        flex-direction: column;
    }
    
    .stripe-integration-sidebar {
        max-width: none;
    }
}
