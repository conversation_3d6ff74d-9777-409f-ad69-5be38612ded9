require('dotenv').config();
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');
const rateLimit = require('express-rate-limit');

// Import routes
const authRoutes = require('./controllers/authController');
const paymentRoutes = require('./controllers/paymentController');
const commissionRoutes = require('./controllers/commissionController');
const webhookRoutes = require('./controllers/webhookController');
const vendorRoutes = require('./controllers/vendorController');

// Import middleware
const errorHandler = require('./middleware/errorHandler');
const authMiddleware = require('./middleware/auth');
const { sanitizeMiddleware } = require('./middleware/validation');
const {
    rateLimits,
    speedLimiter,
    securityHeaders,
    securityLogger,
    validateEnvironment,
    sanitizeInput
} = require('./middleware/security');
const { securityMonitoringMiddleware } = require('./utils/securityAudit');

const app = express();
const PORT = process.env.PORT || 3000;

// Validate environment variables
try {
    validateEnvironment();
} catch (error) {
    console.error('Environment validation failed:', error.message);
    process.exit(1);
}

// Enhanced security middleware
app.use(helmet());
app.use(securityHeaders);
app.use(securityLogger);

// CORS configuration
const corsOptions = {
  origin: process.env.CORS_ORIGIN ? process.env.CORS_ORIGIN.split(',') : ['http://localhost:3000'],
  credentials: true,
  optionsSuccessStatus: 200
};
app.use(cors(corsOptions));

// Enhanced rate limiting and speed limiting
app.use('/api/', rateLimits.general);
app.use('/api/', speedLimiter);

// Compression middleware
app.use(compression());

// Logging middleware
if (process.env.NODE_ENV !== 'test') {
  app.use(morgan('combined'));
}

// Body parsing middleware
// Note: Stripe webhooks need raw body, so we handle this in webhook routes
app.use('/api/webhooks', express.raw({ type: 'application/json' }));
app.use(express.json({ limit: '1mb' })); // Reduced limit for security
app.use(express.urlencoded({ extended: true, limit: '1mb' }));

// Input sanitization middleware
app.use('/api/', sanitizeMiddleware);

// Security monitoring middleware
app.use('/api/', securityMonitoringMiddleware);

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV
  });
});

// API routes with specific rate limits
app.use('/api/auth', rateLimits.auth, authRoutes);
app.use('/api/payments', rateLimits.payment, authMiddleware, paymentRoutes);
app.use('/api/commissions', rateLimits.commission, authMiddleware, commissionRoutes);
app.use('/api/vendors', authMiddleware, vendorRoutes);
app.use('/api/webhooks', rateLimits.webhook, webhookRoutes);

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Route not found',
    message: `Cannot ${req.method} ${req.originalUrl}`
  });
});

// Global error handler
app.use(errorHandler);

// Start server
if (process.env.NODE_ENV !== 'test') {
  app.listen(PORT, () => {
    console.log(`🚀 Server running on port ${PORT}`);
    console.log(`📊 Environment: ${process.env.NODE_ENV}`);
    console.log(`🔗 Health check: http://localhost:${PORT}/health`);
  });
}

module.exports = app;
