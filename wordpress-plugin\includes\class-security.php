<?php
/**
 * Security utilities for Stripe Integration Plugin
 */

if (!defined('ABSPATH')) {
    exit;
}

class Stripe_Integration_Security {
    
    /**
     * Validate and sanitize payment data
     */
    public static function validate_payment_data($data) {
        $errors = array();
        
        // Validate amount
        if (!isset($data['amount']) || !is_numeric($data['amount'])) {
            $errors[] = 'Invalid amount';
        } elseif ($data['amount'] <= 0 || $data['amount'] > 999999.99) {
            $errors[] = 'Amount must be between $0.01 and $999,999.99';
        }
        
        // Validate email
        if (!isset($data['customer_email']) || !is_email($data['customer_email'])) {
            $errors[] = 'Invalid email address';
        }
        
        // Validate name
        if (!isset($data['customer_name']) || empty(trim($data['customer_name']))) {
            $errors[] = 'Customer name is required';
        } elseif (strlen($data['customer_name']) > 100) {
            $errors[] = 'Customer name is too long';
        } elseif (!preg_match('/^[a-zA-Z\s\'-]+$/', $data['customer_name'])) {
            $errors[] = 'Customer name contains invalid characters';
        }
        
        // Validate order ID
        if (!isset($data['order_id']) || empty(trim($data['order_id']))) {
            $errors[] = 'Order ID is required';
        } elseif (!preg_match('/^[a-zA-Z0-9\-_]+$/', $data['order_id'])) {
            $errors[] = 'Order ID contains invalid characters';
        }
        
        return $errors;
    }
    
    /**
     * Sanitize input data
     */
    public static function sanitize_input($data) {
        if (is_array($data)) {
            return array_map(array(self::class, 'sanitize_input'), $data);
        }
        
        if (is_string($data)) {
            // Remove potential XSS patterns
            $data = preg_replace('/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/i', '', $data);
            $data = preg_replace('/javascript:/i', '', $data);
            $data = preg_replace('/on\w+\s*=/i', '', $data);
            $data = sanitize_text_field($data);
        }
        
        return $data;
    }
    
    /**
     * Generate secure nonce for AJAX requests
     */
    public static function generate_nonce($action) {
        return wp_create_nonce($action);
    }
    
    /**
     * Verify nonce for AJAX requests
     */
    public static function verify_nonce($nonce, $action) {
        return wp_verify_nonce($nonce, $action);
    }
    
    /**
     * Check if user has required capabilities
     */
    public static function check_admin_capability() {
        return current_user_can('manage_options');
    }
    
    /**
     * Rate limiting for payment requests
     */
    public static function check_rate_limit($ip_address, $limit = 10, $window = 300) {
        $transient_key = 'stripe_rate_limit_' . md5($ip_address);
        $requests = get_transient($transient_key);
        
        if ($requests === false) {
            $requests = 1;
            set_transient($transient_key, $requests, $window);
            return true;
        }
        
        if ($requests >= $limit) {
            return false;
        }
        
        $requests++;
        set_transient($transient_key, $requests, $window);
        return true;
    }
    
    /**
     * Log security events
     */
    public static function log_security_event($event_type, $details = array()) {
        $log_entry = array(
            'timestamp' => current_time('mysql'),
            'event_type' => $event_type,
            'ip_address' => self::get_client_ip(),
            'user_agent' => isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : '',
            'user_id' => get_current_user_id(),
            'details' => $details
        );
        
        // Store in WordPress options (for simple logging)
        $security_log = get_option('stripe_integration_security_log', array());
        $security_log[] = $log_entry;
        
        // Keep only last 100 entries
        if (count($security_log) > 100) {
            $security_log = array_slice($security_log, -100);
        }
        
        update_option('stripe_integration_security_log', $security_log);
        
        // Also log to WordPress error log if debug is enabled
        if (WP_DEBUG_LOG) {
            error_log('Stripe Integration Security Event: ' . json_encode($log_entry));
        }
    }
    
    /**
     * Get client IP address
     */
    public static function get_client_ip() {
        $ip_keys = array(
            'HTTP_CF_CONNECTING_IP',     // Cloudflare
            'HTTP_CLIENT_IP',
            'HTTP_X_FORWARDED_FOR',
            'HTTP_X_FORWARDED',
            'HTTP_X_CLUSTER_CLIENT_IP',
            'HTTP_FORWARDED_FOR',
            'HTTP_FORWARDED',
            'REMOTE_ADDR'
        );
        
        foreach ($ip_keys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = explode(',', $ip)[0];
                }
                $ip = trim($ip);
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : '0.0.0.0';
    }
    
    /**
     * Validate API configuration
     */
    public static function validate_api_config($config) {
        $errors = array();
        
        // Validate API base URL
        if (empty($config['api_base_url'])) {
            $errors[] = 'API base URL is required';
        } elseif (!filter_var($config['api_base_url'], FILTER_VALIDATE_URL)) {
            $errors[] = 'Invalid API base URL format';
        } elseif (strpos($config['api_base_url'], 'https://') !== 0) {
            $errors[] = 'API base URL must use HTTPS';
        }
        
        // Validate API key
        if (empty($config['api_key'])) {
            $errors[] = 'API key is required';
        } elseif (strlen($config['api_key']) < 32) {
            $errors[] = 'API key appears to be too short';
        }
        
        // Validate vendor ID
        if (empty($config['vendor_id'])) {
            $errors[] = 'Vendor ID is required';
        } elseif (!preg_match('/^[a-f0-9]{8}-[a-f0-9]{4}-4[a-f0-9]{3}-[89ab][a-f0-9]{3}-[a-f0-9]{12}$/i', $config['vendor_id'])) {
            $errors[] = 'Vendor ID must be a valid UUID';
        }
        
        // Validate niche
        $valid_niches = array('grocery', 'catering', 'restaurant', 'retail', 'other');
        if (empty($config['niche']) || !in_array($config['niche'], $valid_niches)) {
            $errors[] = 'Invalid business niche selected';
        }
        
        return $errors;
    }
    
    /**
     * Encrypt sensitive data
     */
    public static function encrypt_data($data) {
        if (!function_exists('openssl_encrypt')) {
            return base64_encode($data); // Fallback to base64 if OpenSSL not available
        }
        
        $key = self::get_encryption_key();
        $iv = openssl_random_pseudo_bytes(16);
        $encrypted = openssl_encrypt($data, 'AES-256-CBC', $key, 0, $iv);
        
        return base64_encode($iv . $encrypted);
    }
    
    /**
     * Decrypt sensitive data
     */
    public static function decrypt_data($encrypted_data) {
        if (!function_exists('openssl_decrypt')) {
            return base64_decode($encrypted_data); // Fallback from base64 if OpenSSL not available
        }
        
        $data = base64_decode($encrypted_data);
        $key = self::get_encryption_key();
        $iv = substr($data, 0, 16);
        $encrypted = substr($data, 16);
        
        return openssl_decrypt($encrypted, 'AES-256-CBC', $key, 0, $iv);
    }
    
    /**
     * Get encryption key
     */
    private static function get_encryption_key() {
        $key = get_option('stripe_integration_encryption_key');
        
        if (!$key) {
            $key = wp_generate_password(32, false);
            update_option('stripe_integration_encryption_key', $key);
        }
        
        return hash('sha256', $key . SECURE_AUTH_KEY);
    }
    
    /**
     * Check for suspicious activity patterns
     */
    public static function check_suspicious_activity($data) {
        $suspicious_patterns = array();
        
        // Check for script injection attempts
        $combined_data = json_encode($data);
        if (preg_match('/<script|javascript:|on\w+\s*=/i', $combined_data)) {
            $suspicious_patterns[] = 'Script injection attempt detected';
        }
        
        // Check for SQL injection patterns
        if (preg_match('/union.*select|drop.*table|insert.*into|update.*set|delete.*from/i', $combined_data)) {
            $suspicious_patterns[] = 'SQL injection attempt detected';
        }
        
        // Check for unusual amount patterns
        if (isset($data['amount'])) {
            if ($data['amount'] > 50000) {
                $suspicious_patterns[] = 'Unusually high payment amount';
            }
            if ($data['amount'] < 0.01) {
                $suspicious_patterns[] = 'Unusually low payment amount';
            }
        }
        
        // Log suspicious activity
        if (!empty($suspicious_patterns)) {
            self::log_security_event('suspicious_activity', array(
                'patterns' => $suspicious_patterns,
                'data' => $data
            ));
        }
        
        return $suspicious_patterns;
    }
    
    /**
     * Generate secure random token
     */
    public static function generate_secure_token($length = 32) {
        if (function_exists('random_bytes')) {
            return bin2hex(random_bytes($length / 2));
        } elseif (function_exists('openssl_random_pseudo_bytes')) {
            return bin2hex(openssl_random_pseudo_bytes($length / 2));
        } else {
            return wp_generate_password($length, false);
        }
    }
    
    /**
     * Validate SSL certificate
     */
    public static function validate_ssl($url) {
        if (strpos($url, 'https://') !== 0) {
            return false;
        }
        
        $context = stream_context_create(array(
            'http' => array(
                'timeout' => 10,
                'method' => 'HEAD'
            ),
            'ssl' => array(
                'verify_peer' => true,
                'verify_peer_name' => true
            )
        ));
        
        $result = @file_get_contents($url, false, $context);
        return $result !== false;
    }
}
