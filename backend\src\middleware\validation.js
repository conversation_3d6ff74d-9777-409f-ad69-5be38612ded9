const Joi = require('joi');

/**
 * Validation middleware factory
 * @param {Object} schema - Joi validation schema
 * @param {string} property - Request property to validate ('body', 'query', 'params')
 * @returns {Function} - Express middleware function
 */
function validate(schema, property = 'body') {
  return (req, res, next) => {
    const { error, value } = schema.validate(req[property], {
      abortEarly: false,
      stripUnknown: true
    });
    
    if (error) {
      const errorDetails = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
        value: detail.context.value
      }));
      
      return res.status(400).json({
        error: 'Validation failed',
        message: 'Request data is invalid',
        details: errorDetails
      });
    }
    
    // Replace request property with validated and sanitized value
    req[property] = value;
    next();
  };
}

// Common validation schemas
const schemas = {
  // Payment validation schemas
  createPaymentIntent: Joi.object({
    amount: Joi.number().positive().required()
      .messages({
        'number.positive': 'Amount must be a positive number',
        'any.required': 'Amount is required'
      }),
    currency: Joi.string().length(3).default('usd')
      .messages({
        'string.length': 'Currency must be a 3-character code'
      }),
    customer_email: Joi.string().email().required()
      .messages({
        'string.email': 'Please provide a valid email address',
        'any.required': 'Customer email is required'
      }),
    customer_name: Joi.string().min(2).max(100).required()
      .messages({
        'string.min': 'Customer name must be at least 2 characters',
        'string.max': 'Customer name cannot exceed 100 characters',
        'any.required': 'Customer name is required'
      }),
    niche: Joi.string().valid('grocery', 'catering', 'restaurant', 'retail', 'other').required()
      .messages({
        'any.only': 'Niche must be one of: grocery, catering, restaurant, retail, other',
        'any.required': 'Niche is required'
      }),
    vendor_id: Joi.string().required()
      .messages({
        'any.required': 'Vendor ID is required'
      }),
    order_id: Joi.string().required()
      .messages({
        'any.required': 'Order ID is required'
      }),
    metadata: Joi.object().default({})
  }),
  
  // Commission validation schemas
  createCommissionRate: Joi.object({
    niche: Joi.string().valid('grocery', 'catering', 'restaurant', 'retail', 'other').required(),
    rate: Joi.number().min(0.001).max(0.5).required()
      .messages({
        'number.min': 'Commission rate must be at least 0.1%',
        'number.max': 'Commission rate cannot exceed 50%',
        'any.required': 'Commission rate is required'
      }),
    min_amount: Joi.number().min(0).default(0),
    max_amount: Joi.number().min(0).allow(null).default(null),
    is_active: Joi.boolean().default(true)
  }),
  
  updateCommissionRate: Joi.object({
    rate: Joi.number().min(0.001).max(0.5),
    min_amount: Joi.number().min(0),
    max_amount: Joi.number().min(0).allow(null),
    is_active: Joi.boolean()
  }).min(1),
  
  // Vendor validation schemas
  createVendor: Joi.object({
    name: Joi.string().min(2).max(100).required(),
    email: Joi.string().email().required(),
    niche: Joi.string().valid('grocery', 'catering', 'restaurant', 'retail', 'other').required(),
    country: Joi.string().length(2).default('US'),
    business_type: Joi.string().valid('individual', 'company').default('individual'),
    metadata: Joi.object().default({})
  }),
  
  updateVendor: Joi.object({
    name: Joi.string().min(2).max(100),
    email: Joi.string().email(),
    niche: Joi.string().valid('grocery', 'catering', 'restaurant', 'retail', 'other'),
    country: Joi.string().length(2),
    business_type: Joi.string().valid('individual', 'company'),
    is_active: Joi.boolean(),
    metadata: Joi.object()
  }).min(1),
  
  // API key validation schemas
  createApiKey: Joi.object({
    wordpress_site: Joi.string().uri().required()
      .messages({
        'string.uri': 'WordPress site must be a valid URL',
        'any.required': 'WordPress site URL is required'
      }),
    permissions: Joi.array().items(
      Joi.string().valid('payments', 'commissions', 'vendors', 'admin')
    ).default(['payments', 'commissions']),
    description: Joi.string().max(255).default('')
  }),
  
  // Webhook validation schemas
  stripeWebhook: Joi.object({
    id: Joi.string().required(),
    object: Joi.string().valid('event').required(),
    type: Joi.string().required(),
    data: Joi.object().required(),
    created: Joi.number().required(),
    livemode: Joi.boolean().required()
  })
};

/**
 * Sanitize input to prevent XSS and injection attacks
 * @param {any} input - Input to sanitize
 * @returns {any} - Sanitized input
 */
function sanitizeInput(input) {
  if (typeof input === 'string') {
    // Remove potentially dangerous characters
    return input
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/javascript:/gi, '')
      .replace(/on\w+\s*=/gi, '')
      .trim();
  }
  
  if (Array.isArray(input)) {
    return input.map(sanitizeInput);
  }
  
  if (typeof input === 'object' && input !== null) {
    const sanitized = {};
    for (const [key, value] of Object.entries(input)) {
      sanitized[key] = sanitizeInput(value);
    }
    return sanitized;
  }
  
  return input;
}

/**
 * Middleware to sanitize request data
 */
function sanitizeMiddleware(req, res, next) {
  if (req.body) {
    req.body = sanitizeInput(req.body);
  }
  if (req.query) {
    req.query = sanitizeInput(req.query);
  }
  if (req.params) {
    req.params = sanitizeInput(req.params);
  }
  next();
}

module.exports = {
  validate,
  schemas,
  sanitizeInput,
  sanitizeMiddleware
};
