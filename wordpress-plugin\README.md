# Stripe Integration WordPress Plugin

A comprehensive WordPress plugin for integrating Stripe payments with commission-based processing through a Node.js backend API.

## Features

- **Secure Payment Processing**: Full Stripe integration with PCI compliance
- **Commission-Based Payments**: Automatic commission calculation based on business niches
- **WordPress Integration**: Native WordPress admin panel and shortcode support
- **Real-time Processing**: Webhook support for instant payment status updates
- **Multi-Niche Support**: Different commission rates for grocery, catering, restaurant, retail, and other businesses
- **Admin Dashboard**: Comprehensive payment tracking and statistics
- **Responsive Design**: Mobile-friendly payment forms and admin interface

## Requirements

- WordPress 5.0 or higher
- PHP 7.4 or higher
- Node.js backend API server (included in this project)
- Stripe account with API keys
- SSL certificate (required for Stripe payments)

## Installation

1. **Upload Plugin Files**
   ```bash
   # Copy the wordpress-plugin directory to your WordPress plugins folder
   cp -r wordpress-plugin /path/to/wordpress/wp-content/plugins/stripe-integration
   ```

2. **Activate Plugin**
   - Go to WordPress Admin → Plugins
   - Find "Stripe Integration" and click "Activate"

3. **Configure Settings**
   - Go to WordPress Admin → Stripe Integration → Settings
   - Enter your backend API URL and API key
   - Set your vendor ID and business niche
   - Save settings

## Configuration

### Backend API Setup

First, ensure your Node.js backend API is running and accessible. The plugin requires:

- **API Base URL**: The URL where your backend API is hosted
- **API Key**: Authentication key generated by your backend
- **Vendor ID**: Your unique vendor identifier in the system
- **Business Niche**: Your business category (affects commission rates)

### Stripe Configuration

The backend API handles Stripe configuration, but you'll need:

- Stripe publishable key (provided by backend)
- Stripe webhook endpoint configured
- Stripe Connect Express account setup

## Usage

### Shortcodes

#### Simple Checkout Button
```php
[stripe_checkout amount="29.99" text="Buy Now" description="Product Name"]
```

#### Full Payment Form
```php
[stripe_payment_form amount="29.99" description="Complete your purchase"]
```

#### Variable Amount Payment Form
```php
[stripe_payment_form show_amount_field="true" description="Enter your payment amount"]
```

### PHP Integration

```php
// Get plugin instance
$stripe_plugin = StripeIntegrationPlugin::get_instance();

// Create payment intent
$result = $stripe_plugin->get_payment_handler()->create_payment_intent(
    29.99,           // amount
    "ORDER-123",     // order_id
    "<EMAIL>", // customer_email
    "John Doe"       // customer_name
);

if ($result['success']) {
    // Handle success
    echo "Payment intent created: " . $result['data']['payment_intent_id'];
} else {
    // Handle error
    echo "Error: " . $result['message'];
}
```

### WordPress Hooks

The plugin provides several WordPress actions for integration:

```php
// Payment succeeded
add_action('stripe_integration_payment_succeeded', function($payment_data) {
    // Handle successful payment
    error_log('Payment succeeded: ' . $payment_data['payment_intent_id']);
});

// Payment failed
add_action('stripe_integration_payment_failed', function($payment_intent_id, $error_message) {
    // Handle failed payment
    error_log('Payment failed: ' . $payment_intent_id . ' - ' . $error_message);
});

// Payment canceled
add_action('stripe_integration_payment_canceled', function($payment_intent_id, $reason) {
    // Handle canceled payment
    error_log('Payment canceled: ' . $payment_intent_id . ' - ' . $reason);
});
```

## Admin Interface

### Dashboard
- Payment statistics and analytics
- Recent transaction overview
- Configuration status
- Quick action buttons

### Settings
- API configuration
- Business niche selection
- Test mode toggle
- Connection testing tools

### Transactions
- Complete payment history
- Transaction details
- Status tracking
- Export capabilities

## Security Features

- **API Key Authentication**: Secure communication with backend
- **Nonce Verification**: WordPress security tokens
- **Input Sanitization**: All user inputs are sanitized
- **Capability Checks**: Admin functions require proper permissions
- **SSL Enforcement**: HTTPS required for payment processing

## Troubleshooting

### Common Issues

1. **"Payment system not configured" Error**
   - Check API base URL and API key in settings
   - Verify backend API is running and accessible
   - Test API connection using admin tools

2. **"Invalid API key" Error**
   - Regenerate API key from backend
   - Ensure API key has proper permissions
   - Check for typos in configuration

3. **Payment Form Not Loading**
   - Verify Stripe.js is loading properly
   - Check browser console for JavaScript errors
   - Ensure SSL certificate is valid

4. **Commission Calculation Issues**
   - Verify business niche is set correctly
   - Check vendor ID configuration
   - Review backend commission rate settings

### Debug Mode

Enable WordPress debug mode to see detailed error logs:

```php
// In wp-config.php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

Logs will be available in `/wp-content/debug.log`

## File Structure

```
wordpress-plugin/
├── stripe-integration.php          # Main plugin file
├── includes/
│   ├── class-api-client.php       # Backend API communication
│   ├── class-payment-handler.php  # Payment processing logic
│   └── class-admin-settings.php   # Admin settings management
├── admin/
│   ├── views/
│   │   ├── dashboard.php          # Admin dashboard
│   │   └── settings.php           # Settings page
│   └── css/
│       └── admin.css              # Admin styles
├── public/
│   ├── views/
│   │   ├── payment-form.php       # Payment form template
│   │   └── checkout-button.php    # Checkout button template
│   ├── js/
│   │   └── stripe-integration.js  # Frontend JavaScript
│   └── css/
│       └── stripe-integration.css # Frontend styles
└── README.md                      # This file
```

## Support

For support and bug reports:

1. Check the WordPress admin dashboard for configuration issues
2. Review the debug logs for error details
3. Test API connectivity using admin tools
4. Verify backend API server status

## License

This plugin is part of the Stripe Integration project and follows the same licensing terms as the overall project.
