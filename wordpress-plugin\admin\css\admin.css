/**
 * Stripe Integration Admin Styles
 */

/* Dashboard Layout */
.stripe-integration-dashboard {
    margin-top: 20px;
}

.stripe-integration-dashboard-section {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.stripe-integration-dashboard-section h2 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #333;
    border-bottom: 2px solid #f0f0f0;
    padding-bottom: 10px;
}

/* Statistics Grid */
.stripe-integration-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 8px;
    text-align: center;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.stat-number {
    font-size: 2.5em;
    font-weight: bold;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.9em;
    opacity: 0.9;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Transactions Table */
.stripe-integration-transactions-table {
    margin-bottom: 20px;
}

.stripe-integration-transactions-table table {
    width: 100%;
}

.stripe-integration-transactions-table th {
    background: #f8f9fa;
    font-weight: 600;
    padding: 12px;
}

.stripe-integration-transactions-table td {
    padding: 12px;
    vertical-align: top;
}

.status-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8em;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-badge.status-pending {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.status-badge.status-succeeded {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-badge.status-failed {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.status-badge.status-canceled {
    background: #e2e3e5;
    color: #383d41;
    border: 1px solid #d6d8db;
}

/* Quick Actions */
.stripe-integration-quick-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 20px;
}

.stripe-integration-quick-actions .button {
    margin: 0;
}

.stripe-integration-quick-action-results {
    margin-top: 15px;
}

.stripe-integration-quick-action-results .notice {
    margin: 0;
}

/* Usage Instructions */
.stripe-integration-usage-instructions {
    line-height: 1.6;
}

.stripe-integration-usage-instructions h3 {
    color: #333;
    margin-top: 25px;
    margin-bottom: 10px;
}

.stripe-integration-usage-instructions h4 {
    color: #555;
    margin-top: 20px;
    margin-bottom: 8px;
}

.shortcode-example {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 15px;
}

.shortcode-example h4 {
    margin-top: 0;
    margin-bottom: 10px;
    color: #495057;
}

.shortcode-example code {
    background: #e9ecef;
    padding: 8px 12px;
    border-radius: 4px;
    display: block;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    color: #495057;
}

.code-example {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 15px;
}

.code-example pre {
    margin: 0;
    background: #2d3748;
    color: #e2e8f0;
    padding: 15px;
    border-radius: 4px;
    overflow-x: auto;
    font-size: 13px;
    line-height: 1.5;
}

.code-example code {
    background: none;
    color: inherit;
    padding: 0;
    font-family: 'Courier New', monospace;
}

/* Admin Container */
.stripe-integration-admin-container {
    display: flex;
    gap: 20px;
    margin-top: 20px;
}

.stripe-integration-main-content {
    flex: 2;
}

.stripe-integration-sidebar {
    flex: 1;
    max-width: 350px;
}

/* Status Cards */
.stripe-integration-status-card,
.stripe-integration-help-card {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.stripe-integration-status-card h3,
.stripe-integration-help-card h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #333;
    border-bottom: 1px solid #eee;
    padding-bottom: 8px;
}

.stripe-integration-status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
}

.stripe-integration-status-item:last-child {
    border-bottom: none;
}

.status-label {
    font-weight: 500;
    color: #555;
}

.status-indicator {
    font-weight: bold;
    font-size: 16px;
}

.status-indicator.success {
    color: #46b450;
}

.status-indicator.error {
    color: #dc3232;
}

.stripe-integration-status-overall {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 2px solid #eee;
    text-align: center;
}

.stripe-integration-test-buttons {
    margin-top: 20px;
}

.stripe-integration-test-buttons button {
    display: block;
    width: 100%;
    margin-bottom: 10px;
}

.stripe-integration-test-results {
    margin-top: 15px;
}

.stripe-integration-test-results .notice {
    margin: 0;
    padding: 10px;
}

/* Settings Form */
.stripe-integration-settings-section {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.stripe-integration-settings-section h2 {
    margin-top: 0;
    margin-bottom: 10px;
    color: #333;
    border-bottom: 2px solid #f0f0f0;
    padding-bottom: 10px;
}

.stripe-integration-field {
    margin-bottom: 15px;
}

/* Help Section */
.stripe-integration-help-card ol {
    padding-left: 20px;
}

.stripe-integration-help-card li {
    margin-bottom: 8px;
    line-height: 1.5;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .stripe-integration-stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 900px) {
    .stripe-integration-admin-container {
        flex-direction: column;
    }
    
    .stripe-integration-sidebar {
        max-width: none;
    }
    
    .stripe-integration-stats-grid {
        grid-template-columns: 1fr;
    }
    
    .stripe-integration-quick-actions {
        flex-direction: column;
    }
    
    .stripe-integration-quick-actions .button {
        width: 100%;
        text-align: center;
    }
}

@media (max-width: 600px) {
    .stripe-integration-dashboard-section {
        padding: 15px;
    }
    
    .stat-card {
        padding: 15px;
    }
    
    .stat-number {
        font-size: 2em;
    }
    
    .stripe-integration-transactions-table {
        overflow-x: auto;
    }
    
    .shortcode-example code {
        font-size: 12px;
        word-break: break-all;
    }
}

/* Loading States */
.stripe-integration-loading {
    opacity: 0.6;
    pointer-events: none;
}

.stripe-integration-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007cba;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 8px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error States */
.stripe-integration-error {
    color: #dc3232;
    font-weight: 500;
}

.stripe-integration-success {
    color: #46b450;
    font-weight: 500;
}

/* Custom Notice Styles */
.stripe-integration-notice {
    padding: 12px 15px;
    margin: 15px 0;
    border-left: 4px solid #ddd;
    background: #fff;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.stripe-integration-notice.notice-success {
    border-left-color: #46b450;
    background: #f0f8f0;
}

.stripe-integration-notice.notice-error {
    border-left-color: #dc3232;
    background: #fdf0f0;
}

.stripe-integration-notice.notice-warning {
    border-left-color: #ffb900;
    background: #fffbf0;
}

.stripe-integration-notice.notice-info {
    border-left-color: #00a0d2;
    background: #f0f8ff;
}
