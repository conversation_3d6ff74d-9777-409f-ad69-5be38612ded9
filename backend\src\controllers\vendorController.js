const express = require('express');
const { validate, schemas } = require('../middleware/validation');
const { asyncHandler, ValidationError, NotFoundError } = require('../middleware/errorHandler');
const { requirePermissions } = require('../middleware/auth');
const vendorService = require('../services/vendorService');

const router = express.Router();

/**
 * Create a new vendor
 * POST /api/vendors
 */
router.post('/',
  requirePermissions(['vendors', 'admin']),
  validate(schemas.createVendor),
  asyncHandler(async (req, res) => {
    const vendor = await vendorService.createVendor(req.body);
    
    res.status(201).json({
      success: true,
      message: 'Vendor created successfully',
      data: vendor
    });
  })
);

/**
 * Get all vendors with filtering and pagination
 * GET /api/vendors
 */
router.get('/',
  asyncHandler(async (req, res) => {
    const result = await vendorService.listVendors(req.query);
    
    res.json({
      success: true,
      data: result
    });
  })
);

/**
 * Get vendor by ID
 * GET /api/vendors/:id
 */
router.get('/:id',
  asyncHandler(async (req, res) => {
    const vendor = await vendorService.getVendor(req.params.id);
    
    res.json({
      success: true,
      data: vendor
    });
  })
);

/**
 * Update vendor
 * PUT /api/vendors/:id
 */
router.put('/:id',
  requirePermissions(['vendors', 'admin']),
  validate(schemas.updateVendor),
  asyncHandler(async (req, res) => {
    const vendor = await vendorService.updateVendor(req.params.id, req.body);
    
    res.json({
      success: true,
      message: 'Vendor updated successfully',
      data: vendor
    });
  })
);

/**
 * Delete vendor (soft delete)
 * DELETE /api/vendors/:id
 */
router.delete('/:id',
  requirePermissions(['vendors', 'admin']),
  asyncHandler(async (req, res) => {
    const vendor = await vendorService.deleteVendor(req.params.id);
    
    res.json({
      success: true,
      message: 'Vendor deleted successfully',
      data: vendor
    });
  })
);

/**
 * Create Stripe Express account for vendor
 * POST /api/vendors/:id/stripe-account
 */
router.post('/:id/stripe-account',
  requirePermissions(['vendors', 'admin']),
  asyncHandler(async (req, res) => {
    const result = await vendorService.createStripeAccount(req.params.id);
    
    res.status(201).json({
      success: true,
      message: 'Stripe account created successfully',
      data: result
    });
  })
);

/**
 * Get vendor Stripe account status
 * GET /api/vendors/:id/stripe-status
 */
router.get('/:id/stripe-status',
  asyncHandler(async (req, res) => {
    const status = await vendorService.getStripeAccountStatus(req.params.id);
    
    res.json({
      success: true,
      data: status
    });
  })
);

/**
 * Generate new onboarding link for vendor
 * POST /api/vendors/:id/onboarding-link
 */
router.post('/:id/onboarding-link',
  requirePermissions(['vendors', 'admin']),
  asyncHandler(async (req, res) => {
    const link = await vendorService.generateOnboardingLink(req.params.id);
    
    res.json({
      success: true,
      message: 'Onboarding link generated successfully',
      data: link
    });
  })
);

/**
 * Get vendor transaction history
 * GET /api/vendors/:id/transactions
 */
router.get('/:id/transactions',
  asyncHandler(async (req, res) => {
    const result = await vendorService.getTransactionHistory(req.params.id, req.query);
    
    res.json({
      success: true,
      data: result
    });
  })
);

/**
 * Stripe onboarding return endpoint
 * GET /api/vendors/:id/stripe/return
 */
router.get('/:id/stripe/return',
  asyncHandler(async (req, res) => {
    // This endpoint is called when vendor completes Stripe onboarding
    const vendor = await vendorService.getVendor(req.params.id);
    
    // You can redirect to a success page or return a success message
    res.send(`
      <!DOCTYPE html>
      <html>
      <head>
        <title>Stripe Onboarding Complete</title>
        <style>
          body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
          .success { color: #28a745; }
          .container { max-width: 600px; margin: 0 auto; }
        </style>
      </head>
      <body>
        <div class="container">
          <h1 class="success">✅ Onboarding Complete!</h1>
          <p>Thank you, <strong>${vendor.name}</strong>! Your Stripe account has been successfully set up.</p>
          <p>You can now receive payments through our platform.</p>
          <p>You may close this window.</p>
        </div>
      </body>
      </html>
    `);
  })
);

/**
 * Stripe onboarding refresh endpoint
 * GET /api/vendors/:id/stripe/refresh
 */
router.get('/:id/stripe/refresh',
  asyncHandler(async (req, res) => {
    // Generate a new onboarding link when the previous one expires
    const link = await vendorService.generateOnboardingLink(req.params.id);
    
    // Redirect to the new onboarding link
    res.redirect(link.onboarding_url);
  })
);

module.exports = router;
