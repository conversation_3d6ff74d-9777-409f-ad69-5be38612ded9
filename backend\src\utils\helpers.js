/**
 * Utility helper functions
 */

/**
 * Format currency amount for display
 * @param {number} amount - Amount in dollars
 * @param {string} currency - Currency code
 * @returns {string} Formatted currency string
 */
function formatCurrency(amount, currency = 'USD') {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency.toUpperCase()
  }).format(amount);
}

/**
 * Convert amount to cents for Stripe
 * @param {number} amount - Amount in dollars
 * @returns {number} Amount in cents
 */
function toCents(amount) {
  return Math.round(amount * 100);
}

/**
 * Convert amount from cents to dollars
 * @param {number} cents - Amount in cents
 * @returns {number} Amount in dollars
 */
function fromCents(cents) {
  return cents / 100;
}

/**
 * Generate a random string for API keys
 * @param {number} length - Length of the string
 * @returns {string} Random string
 */
function generateRandomString(length = 32) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

/**
 * Validate email format
 * @param {string} email - Email to validate
 * @returns {boolean} True if valid email
 */
function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Validate URL format
 * @param {string} url - URL to validate
 * @returns {boolean} True if valid URL
 */
function isValidUrl(url) {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

/**
 * Calculate percentage
 * @param {number} part - Part value
 * @param {number} total - Total value
 * @returns {number} Percentage
 */
function calculatePercentage(part, total) {
  if (total === 0) return 0;
  return (part / total) * 100;
}

/**
 * Round to specified decimal places
 * @param {number} number - Number to round
 * @param {number} decimals - Number of decimal places
 * @returns {number} Rounded number
 */
function roundToDecimals(number, decimals = 2) {
  return Math.round(number * Math.pow(10, decimals)) / Math.pow(10, decimals);
}

/**
 * Sleep for specified milliseconds
 * @param {number} ms - Milliseconds to sleep
 * @returns {Promise} Promise that resolves after sleep
 */
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Retry function with exponential backoff
 * @param {Function} fn - Function to retry
 * @param {number} maxRetries - Maximum number of retries
 * @param {number} baseDelay - Base delay in milliseconds
 * @returns {Promise} Promise that resolves with function result
 */
async function retryWithBackoff(fn, maxRetries = 3, baseDelay = 1000) {
  let lastError;
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;
      
      if (attempt === maxRetries) {
        throw lastError;
      }
      
      const delay = baseDelay * Math.pow(2, attempt);
      await sleep(delay);
    }
  }
}

/**
 * Sanitize string for database storage
 * @param {string} str - String to sanitize
 * @returns {string} Sanitized string
 */
function sanitizeString(str) {
  if (typeof str !== 'string') return str;
  
  return str
    .trim()
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .substring(0, 1000); // Limit length
}

/**
 * Parse and validate JSON safely
 * @param {string} jsonString - JSON string to parse
 * @param {any} defaultValue - Default value if parsing fails
 * @returns {any} Parsed JSON or default value
 */
function safeJsonParse(jsonString, defaultValue = null) {
  try {
    return JSON.parse(jsonString);
  } catch {
    return defaultValue;
  }
}

/**
 * Get niche display name
 * @param {string} niche - Niche code
 * @returns {string} Display name
 */
function getNicheDisplayName(niche) {
  const nicheNames = {
    grocery: 'Grocery',
    catering: 'Catering',
    restaurant: 'Restaurant',
    retail: 'Retail',
    other: 'Other'
  };
  
  return nicheNames[niche] || 'Unknown';
}

/**
 * Get vendor tier display name
 * @param {string} tier - Tier code
 * @returns {string} Display name
 */
function getTierDisplayName(tier) {
  const tierNames = {
    bronze: 'Bronze',
    silver: 'Silver',
    gold: 'Gold',
    platinum: 'Platinum'
  };
  
  return tierNames[tier] || 'Bronze';
}

/**
 * Validate commission rate
 * @param {number} rate - Commission rate to validate
 * @returns {boolean} True if valid rate
 */
function isValidCommissionRate(rate) {
  const minRate = parseFloat(process.env.MIN_COMMISSION_RATE) || 0.001;
  const maxRate = parseFloat(process.env.MAX_COMMISSION_RATE) || 0.5;
  
  return rate >= minRate && rate <= maxRate;
}

/**
 * Format date for display
 * @param {Date|string} date - Date to format
 * @returns {string} Formatted date string
 */
function formatDate(date) {
  return new Date(date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
}

module.exports = {
  formatCurrency,
  toCents,
  fromCents,
  generateRandomString,
  isValidEmail,
  isValidUrl,
  calculatePercentage,
  roundToDecimals,
  sleep,
  retryWithBackoff,
  sanitizeString,
  safeJsonParse,
  getNicheDisplayName,
  getTierDisplayName,
  isValidCommissionRate,
  formatDate
};
