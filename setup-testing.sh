#!/bin/bash

# Stripe Integration Project - Testing Setup Script
# This script helps set up the testing environment

echo "🚀 Setting up Stripe Integration Testing Environment"
echo "=================================================="

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 16+ first."
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm first."
    exit 1
fi

echo "✅ Node.js and npm are installed"

# Navigate to backend directory
cd backend

echo "📦 Installing backend dependencies..."
npm install

# Check if .env file exists
if [ ! -f .env ]; then
    echo "⚙️ Creating .env file template..."
    cat > .env << EOL
# Environment
NODE_ENV=development
PORT=3000

# Database (Replace with your Supabase credentials)
SUPABASE_URL=your_supabase_project_url
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Stripe (Replace with your Stripe test keys)
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# Security (Generate secure random strings)
JWT_SECRET=\$(openssl rand -base64 32 2>/dev/null || echo "your_jwt_secret_here")
API_SECRET_KEY=\$(openssl rand -base64 32 2>/dev/null || echo "your_api_secret_here")
WEBHOOK_SECRET=\$(openssl rand -base64 32 2>/dev/null || echo "your_webhook_secret_here")

# CORS
CORS_ORIGIN=http://localhost:3000,http://localhost:8080

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Optional Security
ENABLE_IP_WHITELIST=false
ENABLE_SIGNATURE_VALIDATION=true
SECURITY_WEBHOOK_URL=
EOL
    echo "✅ Created .env file template"
    echo "⚠️  Please edit .env file with your actual credentials before starting the server"
else
    echo "✅ .env file already exists"
fi

# Install testing dependencies
echo "🧪 Installing testing dependencies..."
npm install --save-dev jest supertest artillery

# Create test script in package.json if it doesn't exist
if ! grep -q '"test"' package.json; then
    echo "📝 Adding test scripts to package.json..."
    # This would need to be done manually or with a more complex script
fi

echo ""
echo "🎉 Backend setup complete!"
echo ""
echo "Next steps:"
echo "1. Edit backend/.env file with your credentials:"
echo "   - Get Supabase URL and key from https://supabase.com"
echo "   - Get Stripe test keys from https://dashboard.stripe.com/test/apikeys"
echo ""
echo "2. Set up database:"
echo "   - Copy SQL from backend/database/schema.sql"
echo "   - Run it in your Supabase SQL editor"
echo ""
echo "3. Start the backend server:"
echo "   cd backend && npm start"
echo ""
echo "4. Test the health endpoint:"
echo "   curl http://localhost:3000/health"
echo ""
echo "5. For WordPress plugin testing:"
echo "   - Copy wordpress-plugin/ to your WordPress wp-content/plugins/"
echo "   - Activate the plugin in WordPress admin"
echo "   - Configure settings in WordPress admin"
echo ""
echo "📖 See TESTING_GUIDE.md for detailed testing instructions"
echo ""

# Check if WordPress directory exists
if [ -d "/var/www/html" ] || [ -d "/Applications/XAMPP" ] || [ -d "/opt/lampp" ]; then
    echo "🔍 WordPress installation detected"
    echo "To install the plugin:"
    echo "sudo cp -r ../wordpress-plugin /path/to/wordpress/wp-content/plugins/stripe-integration"
fi

echo "Happy testing! 🧪✨"
