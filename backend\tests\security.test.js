/**
 * Security Tests for Stripe Integration API
 */

const request = require('supertest');
const app = require('../src/server');
const { SECURITY_EVENTS, RISK_LEVELS } = require('../src/utils/securityAudit');

describe('Security Middleware Tests', () => {
    
    describe('Rate Limiting', () => {
        it('should enforce general rate limits', async () => {
            const requests = [];
            
            // Make multiple requests to exceed rate limit
            for (let i = 0; i < 105; i++) {
                requests.push(
                    request(app)
                        .get('/api/health')
                        .set('X-Forwarded-For', '*************')
                );
            }
            
            const responses = await Promise.all(requests);
            const rateLimitedResponses = responses.filter(res => res.status === 429);
            
            expect(rateLimitedResponses.length).toBeGreaterThan(0);
        });
        
        it('should enforce payment-specific rate limits', async () => {
            const requests = [];
            
            // Make multiple payment requests to exceed rate limit
            for (let i = 0; i < 15; i++) {
                requests.push(
                    request(app)
                        .post('/api/payments/create-intent')
                        .set('X-Forwarded-For', '*************')
                        .set('Authorization', 'Bearer valid-token')
                        .send({
                            amount: 100,
                            vendor_id: 'test-vendor-id',
                            order_id: `order-${i}`,
                            customer_email: '<EMAIL>',
                            customer_name: 'Test Customer'
                        })
                );
            }
            
            const responses = await Promise.all(requests);
            const rateLimitedResponses = responses.filter(res => res.status === 429);
            
            expect(rateLimitedResponses.length).toBeGreaterThan(0);
        });
    });
    
    describe('Input Sanitization', () => {
        it('should sanitize XSS attempts in request body', async () => {
            const maliciousPayload = {
                amount: 100,
                vendor_id: 'test-vendor',
                order_id: '<script>alert("xss")</script>',
                customer_email: '<EMAIL>',
                customer_name: 'Test<script>alert("xss")</script>Customer'
            };
            
            const response = await request(app)
                .post('/api/payments/create-intent')
                .set('Authorization', 'Bearer valid-token')
                .send(maliciousPayload);
            
            // Should not contain script tags in response
            expect(JSON.stringify(response.body)).not.toMatch(/<script/i);
        });
        
        it('should sanitize SQL injection attempts', async () => {
            const maliciousPayload = {
                amount: 100,
                vendor_id: "'; DROP TABLE users; --",
                order_id: 'test-order',
                customer_email: '<EMAIL>',
                customer_name: 'Test Customer'
            };
            
            const response = await request(app)
                .post('/api/payments/create-intent')
                .set('Authorization', 'Bearer valid-token')
                .send(maliciousPayload);
            
            // Should return validation error, not execute SQL
            expect(response.status).toBe(400);
        });
    });
    
    describe('Request Signature Validation', () => {
        it('should reject requests with invalid signatures', async () => {
            const response = await request(app)
                .post('/api/payments/create-intent')
                .set('Authorization', 'Bearer valid-token')
                .set('X-Signature', 'invalid-signature')
                .send({
                    amount: 100,
                    vendor_id: 'test-vendor',
                    order_id: 'test-order',
                    customer_email: '<EMAIL>',
                    customer_name: 'Test Customer'
                });
            
            expect(response.status).toBe(401);
            expect(response.body.error).toMatch(/signature/i);
        });
        
        it('should accept requests with valid signatures', async () => {
            const crypto = require('crypto');
            const payload = JSON.stringify({
                amount: 100,
                vendor_id: 'test-vendor',
                order_id: 'test-order',
                customer_email: '<EMAIL>',
                customer_name: 'Test Customer'
            });
            
            const signature = crypto
                .createHmac('sha256', process.env.WEBHOOK_SECRET || 'test-secret')
                .update(payload)
                .digest('hex');
            
            const response = await request(app)
                .post('/api/payments/create-intent')
                .set('Authorization', 'Bearer valid-token')
                .set('X-Signature', `sha256=${signature}`)
                .send(JSON.parse(payload));
            
            expect(response.status).not.toBe(401);
        });
    });
    
    describe('Security Headers', () => {
        it('should include security headers in responses', async () => {
            const response = await request(app)
                .get('/health');
            
            expect(response.headers['x-content-type-options']).toBe('nosniff');
            expect(response.headers['x-frame-options']).toBe('DENY');
            expect(response.headers['x-xss-protection']).toBe('1; mode=block');
            expect(response.headers['strict-transport-security']).toBeDefined();
        });
        
        it('should include CSP headers', async () => {
            const response = await request(app)
                .get('/health');
            
            expect(response.headers['content-security-policy']).toBeDefined();
        });
    });
    
    describe('Environment Validation', () => {
        it('should validate required environment variables', () => {
            const { validateEnvironment } = require('../src/middleware/security');
            
            // Temporarily remove required env var
            const originalValue = process.env.SUPABASE_URL;
            delete process.env.SUPABASE_URL;
            
            expect(() => validateEnvironment()).toThrow();
            
            // Restore env var
            process.env.SUPABASE_URL = originalValue;
        });
    });
    
    describe('Payment Fraud Detection', () => {
        it('should detect unusually high payment amounts', async () => {
            const response = await request(app)
                .post('/api/payments/create-intent')
                .set('Authorization', 'Bearer valid-token')
                .send({
                    amount: 999999, // Very high amount
                    vendor_id: 'test-vendor',
                    order_id: 'test-order',
                    customer_email: '<EMAIL>',
                    customer_name: 'Test Customer'
                });
            
            // Should trigger fraud detection
            expect(response.status).toBe(400);
        });
        
        it('should detect rapid successive payments', async () => {
            const requests = [];
            
            // Make multiple rapid payments
            for (let i = 0; i < 5; i++) {
                requests.push(
                    request(app)
                        .post('/api/payments/create-intent')
                        .set('Authorization', 'Bearer valid-token')
                        .send({
                            amount: 100,
                            vendor_id: 'test-vendor',
                            order_id: `rapid-order-${i}`,
                            customer_email: '<EMAIL>',
                            customer_name: 'Rapid Customer'
                        })
                );
            }
            
            const responses = await Promise.all(requests);
            const blockedResponses = responses.filter(res => res.status === 400);
            
            expect(blockedResponses.length).toBeGreaterThan(0);
        });
    });
    
    describe('API Abuse Detection', () => {
        it('should detect and block API abuse patterns', async () => {
            const requests = [];
            
            // Make many requests from same IP
            for (let i = 0; i < 120; i++) {
                requests.push(
                    request(app)
                        .get('/api/health')
                        .set('X-Forwarded-For', '*************')
                );
            }
            
            const responses = await Promise.all(requests);
            const blockedResponses = responses.filter(res => res.status === 429);
            
            expect(blockedResponses.length).toBeGreaterThan(0);
        });
    });
    
    describe('Input Validation', () => {
        it('should validate payment amounts', async () => {
            const invalidAmounts = [-100, 0, 1000000, 'invalid', null];
            
            for (const amount of invalidAmounts) {
                const response = await request(app)
                    .post('/api/payments/create-intent')
                    .set('Authorization', 'Bearer valid-token')
                    .send({
                        amount: amount,
                        vendor_id: 'test-vendor',
                        order_id: 'test-order',
                        customer_email: '<EMAIL>',
                        customer_name: 'Test Customer'
                    });
                
                expect(response.status).toBe(400);
            }
        });
        
        it('should validate email addresses', async () => {
            const invalidEmails = ['invalid-email', '@example.com', 'test@', 'test.com'];
            
            for (const email of invalidEmails) {
                const response = await request(app)
                    .post('/api/payments/create-intent')
                    .set('Authorization', 'Bearer valid-token')
                    .send({
                        amount: 100,
                        vendor_id: 'test-vendor',
                        order_id: 'test-order',
                        customer_email: email,
                        customer_name: 'Test Customer'
                    });
                
                expect(response.status).toBe(400);
            }
        });
        
        it('should validate UUID formats', async () => {
            const invalidUUIDs = ['invalid-uuid', '123', 'not-a-uuid', ''];
            
            for (const uuid of invalidUUIDs) {
                const response = await request(app)
                    .post('/api/payments/create-intent')
                    .set('Authorization', 'Bearer valid-token')
                    .send({
                        amount: 100,
                        vendor_id: uuid,
                        order_id: 'test-order',
                        customer_email: '<EMAIL>',
                        customer_name: 'Test Customer'
                    });
                
                expect(response.status).toBe(400);
            }
        });
    });
    
    describe('Security Logging', () => {
        it('should log security events', async () => {
            const { logSecurityEvent } = require('../src/utils/securityAudit');
            
            const result = await logSecurityEvent(
                SECURITY_EVENTS.SUSPICIOUS_ACTIVITY,
                RISK_LEVELS.MEDIUM,
                { test: 'security logging' }
            );
            
            expect(result).toBe(true);
        });
    });
});

describe('WordPress Plugin Security Tests', () => {
    // These would be integration tests with WordPress
    // For now, we'll test the security functions directly
    
    describe('Input Sanitization', () => {
        it('should sanitize malicious input', () => {
            // This would require WordPress environment
            // For now, just test the concept
            const maliciousInput = '<script>alert("xss")</script>';
            const sanitized = maliciousInput.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
            
            expect(sanitized).not.toContain('<script>');
        });
    });
    
    describe('Rate Limiting', () => {
        it('should implement rate limiting for payment requests', () => {
            // Test rate limiting logic
            const rateLimitKey = 'test_ip_' + Date.now();
            
            // Simulate multiple requests
            let allowed = true;
            for (let i = 0; i < 15; i++) {
                if (i >= 10) {
                    allowed = false; // Should be rate limited after 10 requests
                }
            }
            
            expect(allowed).toBe(false);
        });
    });
});
