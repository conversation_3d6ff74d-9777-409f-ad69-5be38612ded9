/**
 * Stripe Integration Frontend JavaScript
 */

(function($) {
    'use strict';
    
    // Global Stripe Integration object
    window.StripeIntegration = {
        stripe: null,
        elements: {},
        cards: {},
        
        /**
         * Initialize Stripe
         */
        init: function() {
            if (typeof Stripe === 'undefined') {
                console.error('Stripe.js not loaded');
                return false;
            }
            
            // Initialize Stripe (publishable key will be set by backend)
            // For now, we'll get the publishable key from the payment intent
            this.stripe = null; // Will be initialized when creating payment intent
            
            return true;
        },
        
        /**
         * Initialize payment form
         */
        initPaymentForm: function(formId) {
            var self = this;
            var form = $('#' + formId);
            
            if (form.length === 0) {
                console.error('Payment form not found: ' + formId);
                return;
            }
            
            // Handle form submission
            form.on('submit', function(e) {
                e.preventDefault();
                self.handlePaymentSubmit(formId);
            });
            
            // Auto-generate order ID if empty
            var orderIdField = form.find('input[name="order_id"]');
            if (orderIdField.length && !orderIdField.val()) {
                orderIdField.val('ORDER-' + Date.now());
            }
        },
        
        /**
         * Handle payment form submission
         */
        handlePaymentSubmit: function(formId) {
            var self = this;
            var form = $('#' + formId);
            var submitBtn = form.find('.stripe-payment-submit-btn');
            var messagesDiv = form.find('.stripe-payment-messages');
            
            // Validate form
            if (!this.validateForm(form)) {
                return;
            }
            
            // Disable submit button and show loading
            this.setSubmitButtonState(submitBtn, true);
            messagesDiv.empty();
            
            // Get form data
            var formData = {
                amount: parseFloat(form.find('input[name="amount"]').val()),
                customer_name: form.find('input[name="customer_name"]').val(),
                customer_email: form.find('input[name="customer_email"]').val(),
                order_id: form.find('input[name="order_id"]').val() || 'ORDER-' + Date.now(),
                action: 'stripe_create_payment_intent',
                nonce: form.find('input[name="nonce"]').val()
            };
            
            // Create payment intent
            $.ajax({
                url: stripe_integration_ajax.ajax_url,
                type: 'POST',
                data: formData,
                success: function(response) {
                    if (response.success) {
                        self.handlePaymentIntentCreated(formId, response.data);
                    } else {
                        self.showError(messagesDiv, response.data || 'Payment failed');
                        self.setSubmitButtonState(submitBtn, false);
                    }
                },
                error: function() {
                    self.showError(messagesDiv, 'Network error. Please try again.');
                    self.setSubmitButtonState(submitBtn, false);
                }
            });
        },
        
        /**
         * Handle payment intent creation success
         */
        handlePaymentIntentCreated: function(formId, paymentData) {
            var self = this;
            var form = $('#' + formId);
            var submitBtn = form.find('.stripe-payment-submit-btn');
            var messagesDiv = form.find('.stripe-payment-messages');
            
            // Initialize Stripe with the publishable key from response
            if (!this.stripe && paymentData.publishable_key) {
                this.stripe = Stripe(paymentData.publishable_key);
            }
            
            if (!this.stripe) {
                this.showError(messagesDiv, 'Payment system configuration error');
                this.setSubmitButtonState(submitBtn, false);
                return;
            }
            
            // Create or get Stripe Elements
            if (!this.elements[formId]) {
                this.elements[formId] = this.stripe.elements();
                this.createCardElement(formId);
            }
            
            // Confirm payment with Stripe
            this.stripe.confirmCardPayment(paymentData.client_secret, {
                payment_method: {
                    card: this.cards[formId],
                    billing_details: {
                        name: form.find('input[name="customer_name"]').val(),
                        email: form.find('input[name="customer_email"]').val()
                    }
                }
            }).then(function(result) {
                if (result.error) {
                    // Payment failed
                    self.showError(messagesDiv, result.error.message);
                    self.setSubmitButtonState(submitBtn, false);
                } else {
                    // Payment succeeded
                    self.handlePaymentSuccess(formId, result.paymentIntent);
                }
            });
        },
        
        /**
         * Create Stripe card element
         */
        createCardElement: function(formId) {
            var cardElementId = formId + '_card_element';
            var cardErrorsId = formId + '_card_errors';
            
            // Create card element
            var card = this.elements[formId].create('card', {
                style: {
                    base: {
                        fontSize: '16px',
                        color: '#424770',
                        '::placeholder': {
                            color: '#aab7c4',
                        },
                    },
                    invalid: {
                        color: '#9e2146',
                    },
                },
            });
            
            // Mount card element
            card.mount('#' + cardElementId);
            
            // Store card reference
            this.cards[formId] = card;
            
            // Handle real-time validation errors from the card Element
            var self = this;
            card.on('change', function(event) {
                var displayError = document.getElementById(cardErrorsId);
                if (event.error) {
                    displayError.textContent = event.error.message;
                } else {
                    displayError.textContent = '';
                }
            });
        },
        
        /**
         * Handle successful payment
         */
        handlePaymentSuccess: function(formId, paymentIntent) {
            var self = this;
            var form = $('#' + formId);
            var submitBtn = form.find('.stripe-payment-submit-btn');
            var messagesDiv = form.find('.stripe-payment-messages');
            
            // Confirm payment with backend
            $.ajax({
                url: stripe_integration_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'stripe_confirm_payment',
                    payment_intent_id: paymentIntent.id,
                    nonce: form.find('input[name="nonce"]').val()
                },
                success: function(response) {
                    if (response.success) {
                        self.showSuccess(messagesDiv, 'Payment successful! Thank you for your purchase.');
                        self.resetForm(form);
                        
                        // Trigger custom event
                        $(document).trigger('stripe_integration_payment_success', [paymentIntent, response.data]);
                    } else {
                        self.showError(messagesDiv, 'Payment processing error: ' + response.data);
                    }
                    self.setSubmitButtonState(submitBtn, false);
                },
                error: function() {
                    self.showError(messagesDiv, 'Payment confirmation failed. Please contact support.');
                    self.setSubmitButtonState(submitBtn, false);
                }
            });
        },
        
        /**
         * Validate form data
         */
        validateForm: function(form) {
            var isValid = true;
            var firstErrorField = null;
            
            // Check required fields
            form.find('input[required]').each(function() {
                var field = $(this);
                var value = field.val().trim();
                
                if (!value) {
                    field.addClass('error');
                    isValid = false;
                    if (!firstErrorField) {
                        firstErrorField = field;
                    }
                } else {
                    field.removeClass('error');
                }
            });
            
            // Validate email
            var emailField = form.find('input[name="customer_email"]');
            if (emailField.length && emailField.val()) {
                var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(emailField.val())) {
                    emailField.addClass('error');
                    isValid = false;
                    if (!firstErrorField) {
                        firstErrorField = emailField;
                    }
                }
            }
            
            // Validate amount
            var amountField = form.find('input[name="amount"]');
            if (amountField.length && amountField.val()) {
                var amount = parseFloat(amountField.val());
                if (isNaN(amount) || amount <= 0) {
                    amountField.addClass('error');
                    isValid = false;
                    if (!firstErrorField) {
                        firstErrorField = amountField;
                    }
                }
            }
            
            // Focus first error field
            if (firstErrorField) {
                firstErrorField.focus();
            }
            
            return isValid;
        },
        
        /**
         * Set submit button state
         */
        setSubmitButtonState: function(button, loading) {
            if (loading) {
                button.prop('disabled', true);
                button.find('.btn-text').hide();
                button.find('.btn-spinner').show();
            } else {
                button.prop('disabled', false);
                button.find('.btn-text').show();
                button.find('.btn-spinner').hide();
            }
        },
        
        /**
         * Show error message
         */
        showError: function(container, message) {
            container.html('<div class="stripe-error">' + this.escapeHtml(message) + '</div>');
            container.get(0).scrollIntoView({ behavior: 'smooth', block: 'center' });
        },
        
        /**
         * Show success message
         */
        showSuccess: function(container, message) {
            container.html('<div class="stripe-success">' + this.escapeHtml(message) + '</div>');
            container.get(0).scrollIntoView({ behavior: 'smooth', block: 'center' });
        },
        
        /**
         * Reset form after successful payment
         */
        resetForm: function(form) {
            form[0].reset();
            form.find('.error').removeClass('error');
            
            // Clear Stripe card element
            var formId = form.find('input[name="form_id"]').val();
            if (this.cards[formId]) {
                this.cards[formId].clear();
            }
        },
        
        /**
         * Escape HTML to prevent XSS
         */
        escapeHtml: function(text) {
            var map = {
                '&': '&amp;',
                '<': '&lt;',
                '>': '&gt;',
                '"': '&quot;',
                "'": '&#039;'
            };
            return text.replace(/[&<>"']/g, function(m) { return map[m]; });
        }
    };
    
    // Initialize when document is ready
    $(document).ready(function() {
        StripeIntegration.init();
    });
    
})(jQuery);
