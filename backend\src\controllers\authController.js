const express = require('express');
const bcrypt = require('bcryptjs');
const { generateToken, generateApiKey } = require('../middleware/auth');
const { validate, schemas } = require('../middleware/validation');
const { asyncHandler, UnauthorizedError, ValidationError } = require('../middleware/errorHandler');
const { supabase } = require('../config/database');

const router = express.Router();

/**
 * Generate API key for WordPress integration
 * POST /api/auth/generate-api-key
 */
router.post('/generate-api-key', 
  validate(schemas.createApiKey),
  asyncHandler(async (req, res) => {
    const { wordpress_site, permissions, description } = req.body;
    
    // For now, we'll create a simple user record for the WordPress site
    // In a production environment, you might want proper user authentication
    const { data: existingUser, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('wordpress_site', wordpress_site)
      .single();
    
    let userId;
    
    if (userError && userError.code === 'PGRST116') {
      // User doesn't exist, create one
      const { data: newUser, error: createError } = await supabase
        .from('users')
        .insert({
          wordpress_site,
          email: `admin@${new URL(wordpress_site).hostname}`,
          created_at: new Date().toISOString()
        })
        .select()
        .single();
      
      if (createError) {
        throw new ValidationError('Failed to create user for WordPress site');
      }
      
      userId = newUser.id;
    } else if (userError) {
      throw userError;
    } else {
      userId = existingUser.id;
    }
    
    // Generate API key
    const apiKey = await generateApiKey({
      user_id: userId,
      wordpress_site,
      permissions,
      description
    });
    
    res.status(201).json({
      success: true,
      message: 'API key generated successfully',
      data: {
        api_key: apiKey,
        permissions,
        wordpress_site,
        expires_in: 'never' // API keys don't expire by default
      }
    });
  })
);

/**
 * Validate API key
 * POST /api/auth/validate-api-key
 */
router.post('/validate-api-key', asyncHandler(async (req, res) => {
  const apiKey = req.headers['x-api-key'];
  
  if (!apiKey) {
    throw new UnauthorizedError('API key is required');
  }
  
  // Check API key in database
  const { data: apiKeyData, error } = await supabase
    .from('api_keys')
    .select(`
      *,
      users (
        id,
        wordpress_site,
        email
      )
    `)
    .eq('key_hash', apiKey)
    .eq('is_active', true)
    .single();
  
  if (error || !apiKeyData) {
    throw new UnauthorizedError('Invalid or inactive API key');
  }
  
  // Update last used timestamp
  await supabase
    .from('api_keys')
    .update({ last_used_at: new Date().toISOString() })
    .eq('id', apiKeyData.id);
  
  res.json({
    success: true,
    message: 'API key is valid',
    data: {
      user_id: apiKeyData.user_id,
      permissions: apiKeyData.permissions,
      wordpress_site: apiKeyData.users.wordpress_site,
      last_used: apiKeyData.last_used_at
    }
  });
}));

/**
 * Revoke API key
 * DELETE /api/auth/revoke-api-key
 */
router.delete('/revoke-api-key', asyncHandler(async (req, res) => {
  const apiKey = req.headers['x-api-key'];
  
  if (!apiKey) {
    throw new UnauthorizedError('API key is required');
  }
  
  // Deactivate API key
  const { data, error } = await supabase
    .from('api_keys')
    .update({ 
      is_active: false,
      revoked_at: new Date().toISOString()
    })
    .eq('key_hash', apiKey)
    .select()
    .single();
  
  if (error || !data) {
    throw new UnauthorizedError('Invalid API key');
  }
  
  res.json({
    success: true,
    message: 'API key revoked successfully'
  });
}));

/**
 * List API keys for a WordPress site
 * GET /api/auth/api-keys
 */
router.get('/api-keys', asyncHandler(async (req, res) => {
  const { wordpress_site } = req.query;
  
  if (!wordpress_site) {
    throw new ValidationError('WordPress site URL is required');
  }
  
  // Get user for the WordPress site
  const { data: user, error: userError } = await supabase
    .from('users')
    .select('id')
    .eq('wordpress_site', wordpress_site)
    .single();
  
  if (userError) {
    throw new UnauthorizedError('WordPress site not found');
  }
  
  // Get API keys for the user
  const { data: apiKeys, error } = await supabase
    .from('api_keys')
    .select('id, permissions, description, is_active, created_at, last_used_at')
    .eq('user_id', user.id)
    .order('created_at', { ascending: false });
  
  if (error) {
    throw error;
  }
  
  res.json({
    success: true,
    data: apiKeys
  });
}));

/**
 * Health check for auth service
 * GET /api/auth/health
 */
router.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'Auth service is healthy',
    timestamp: new Date().toISOString()
  });
});

module.exports = router;
