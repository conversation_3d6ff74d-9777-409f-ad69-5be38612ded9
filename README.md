# Stripe Integration with WordPress Plugin & Backend API

This project provides a comprehensive solution for integrating Stripe payments with WordPress through a secure backend API. The system handles commission calculations, vendor payouts via Stripe Connect, and ensures secure transaction processing.

## Project Structure

```
├── backend/                    # Node.js Express API server
│   ├── src/
│   │   ├── controllers/       # API route controllers
│   │   ├── middleware/        # Authentication & validation middleware
│   │   ├── models/           # Database models
│   │   ├── services/         # Business logic services
│   │   ├── utils/            # Utility functions
│   │   └── config/           # Configuration files
│   ├── tests/                # Backend tests
│   ├── package.json
│   └── .env.example
├── wordpress-plugin/          # WordPress plugin
│   ├── admin/                # Admin panel functionality
│   ├── includes/             # Core plugin functionality
│   ├── public/               # Public-facing functionality
│   ├── assets/               # CSS, JS, images
│   └── stripe-integration.php # Main plugin file
├── docs/                     # Documentation
└── README.md
```

## Key Features

- **Secure Payment Processing**: Stripe integration with Express accounts
- **Commission Management**: Dynamic commission rates based on niche
- **WordPress Integration**: Seamless checkout experience
- **Scalable Architecture**: Easy expansion to other platforms
- **Security First**: No sensitive data exposure, secure API communication

## Quick Start

### Backend Setup
```bash
cd backend
npm install
cp .env.example .env
# Configure your environment variables
npm run dev
```

### WordPress Plugin Setup
1. Copy the `wordpress-plugin` folder to your WordPress `wp-content/plugins/` directory
2. Activate the plugin in WordPress admin
3. Configure Stripe settings in the admin panel

## Environment Variables

### Backend (.env)
```
NODE_ENV=development
PORT=3000
STRIPE_SECRET_KEY=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...
SUPABASE_URL=https://...
SUPABASE_ANON_KEY=...
JWT_SECRET=your-jwt-secret
API_BASE_URL=http://localhost:3000
```

## Security Considerations

- All API communication uses HTTPS
- JWT token-based authentication
- Input validation and sanitization
- No sensitive data stored in WordPress
- Stripe.js for secure card handling

## Commission Structure

The system supports dynamic commission rates based on:
- Niche type (Grocery, Catering, etc.)
- Transaction amount
- Vendor tier

## Testing

Use Stripe's test mode for development:
- Test card: ****************
- Any future expiry date
- Any 3-digit CVC

## Deployment

1. Set up production environment variables
2. Configure live Stripe keys
3. Deploy backend to your preferred hosting service
4. Install WordPress plugin on production site

## Support

For issues and questions, please refer to the documentation in the `docs/` folder.
