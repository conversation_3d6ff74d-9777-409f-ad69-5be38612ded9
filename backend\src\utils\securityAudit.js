/**
 * Security Audit and Monitoring System
 */

const logger = require('./logger');
const { createClient } = require('@supabase/supabase-js');

const supabase = createClient(
    process.env.SUPABASE_URL,
    process.env.SUPABASE_SERVICE_ROLE_KEY
);

/**
 * Security event types
 */
const SECURITY_EVENTS = {
    AUTHENTICATION_FAILURE: 'authentication_failure',
    AUTHORIZATION_FAILURE: 'authorization_failure',
    RATE_LIMIT_EXCEEDED: 'rate_limit_exceeded',
    SUSPICIOUS_ACTIVITY: 'suspicious_activity',
    DATA_BREACH_ATTEMPT: 'data_breach_attempt',
    INVALID_INPUT: 'invalid_input',
    API_ABUSE: 'api_abuse',
    PAYMENT_FRAUD_ATTEMPT: 'payment_fraud_attempt'
};

/**
 * Risk levels
 */
const RISK_LEVELS = {
    LOW: 'low',
    MEDIUM: 'medium',
    HIGH: 'high',
    CRITICAL: 'critical'
};

/**
 * Log security event
 */
async function logSecurityEvent(eventType, riskLevel, details, req = null) {
    const event = {
        event_type: eventType,
        risk_level: riskLevel,
        timestamp: new Date().toISOString(),
        ip_address: req ? (req.ip || req.connection.remoteAddress) : null,
        user_agent: req ? req.get('User-Agent') : null,
        endpoint: req ? req.path : null,
        method: req ? req.method : null,
        details: details || {},
        session_id: req ? req.sessionID : null,
        user_id: req ? req.user?.id : null
    };

    try {
        // Log to database
        await supabase
            .from('security_audit_log')
            .insert([event]);

        // Log to application logger
        logger.warn('Security Event', event);

        // Send alerts for high-risk events
        if (riskLevel === RISK_LEVELS.HIGH || riskLevel === RISK_LEVELS.CRITICAL) {
            await sendSecurityAlert(event);
        }

        return true;
    } catch (error) {
        logger.error('Failed to log security event', { error: error.message, event });
        return false;
    }
}

/**
 * Send security alert
 */
async function sendSecurityAlert(event) {
    try {
        // In a real implementation, you would send alerts via:
        // - Email
        // - Slack/Discord webhook
        // - SMS
        // - Push notification service
        
        logger.error('SECURITY ALERT', {
            type: event.event_type,
            risk: event.risk_level,
            ip: event.ip_address,
            endpoint: event.endpoint,
            details: event.details,
            timestamp: event.timestamp
        });

        // Example: Send to monitoring service
        if (process.env.SECURITY_WEBHOOK_URL) {
            const fetch = require('node-fetch');
            await fetch(process.env.SECURITY_WEBHOOK_URL, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    text: `🚨 Security Alert: ${event.event_type}`,
                    attachments: [{
                        color: event.risk_level === RISK_LEVELS.CRITICAL ? 'danger' : 'warning',
                        fields: [
                            { title: 'Risk Level', value: event.risk_level, short: true },
                            { title: 'IP Address', value: event.ip_address, short: true },
                            { title: 'Endpoint', value: event.endpoint, short: true },
                            { title: 'Time', value: event.timestamp, short: true }
                        ]
                    }]
                })
            });
        }
    } catch (error) {
        logger.error('Failed to send security alert', { error: error.message });
    }
}

/**
 * Analyze request for suspicious patterns
 */
function analyzeRequest(req) {
    const suspiciousPatterns = [];
    const userAgent = req.get('User-Agent') || '';
    const referer = req.get('Referer') || '';

    // Check for common attack patterns
    const maliciousPatterns = [
        /script.*?>/i,
        /javascript:/i,
        /vbscript:/i,
        /onload=/i,
        /onerror=/i,
        /eval\(/i,
        /union.*select/i,
        /drop.*table/i,
        /insert.*into/i,
        /update.*set/i,
        /delete.*from/i,
        /<iframe/i,
        /<object/i,
        /<embed/i
    ];

    // Check request body
    const requestBody = JSON.stringify(req.body || {});
    maliciousPatterns.forEach(pattern => {
        if (pattern.test(requestBody)) {
            suspiciousPatterns.push(`Malicious pattern in body: ${pattern}`);
        }
    });

    // Check query parameters
    const queryString = JSON.stringify(req.query || {});
    maliciousPatterns.forEach(pattern => {
        if (pattern.test(queryString)) {
            suspiciousPatterns.push(`Malicious pattern in query: ${pattern}`);
        }
    });

    // Check User-Agent for bots/scanners
    const botPatterns = [
        /bot/i,
        /crawler/i,
        /spider/i,
        /scanner/i,
        /curl/i,
        /wget/i,
        /python/i,
        /perl/i
    ];

    botPatterns.forEach(pattern => {
        if (pattern.test(userAgent)) {
            suspiciousPatterns.push(`Bot/Scanner detected: ${userAgent}`);
        }
    });

    // Check for missing or suspicious User-Agent
    if (!userAgent || userAgent.length < 10) {
        suspiciousPatterns.push('Missing or suspicious User-Agent');
    }

    // Check for suspicious referers
    if (referer && !referer.startsWith('https://')) {
        suspiciousPatterns.push('Non-HTTPS referer');
    }

    return suspiciousPatterns;
}

/**
 * Monitor payment fraud patterns
 */
async function monitorPaymentFraud(paymentData, req) {
    const fraudIndicators = [];

    // Check for unusual amounts
    if (paymentData.amount > 10000) {
        fraudIndicators.push('High amount transaction');
    }

    if (paymentData.amount < 0.01) {
        fraudIndicators.push('Extremely low amount transaction');
    }

    // Check for rapid successive payments
    try {
        const recentPayments = await supabase
            .from('transactions')
            .select('*')
            .eq('customer_email', paymentData.customer_email)
            .gte('created_at', new Date(Date.now() - 5 * 60 * 1000).toISOString()) // Last 5 minutes
            .order('created_at', { ascending: false });

        if (recentPayments.data && recentPayments.data.length > 3) {
            fraudIndicators.push('Multiple rapid payments from same email');
        }
    } catch (error) {
        logger.error('Error checking recent payments', { error: error.message });
    }

    // Check IP-based patterns
    try {
        const ipPayments = await supabase
            .from('security_audit_log')
            .select('*')
            .eq('ip_address', req.ip)
            .eq('event_type', 'payment_created')
            .gte('timestamp', new Date(Date.now() - 60 * 60 * 1000).toISOString()) // Last hour
            .order('timestamp', { ascending: false });

        if (ipPayments.data && ipPayments.data.length > 10) {
            fraudIndicators.push('Multiple payments from same IP');
        }
    } catch (error) {
        logger.error('Error checking IP payments', { error: error.message });
    }

    // Log fraud indicators if found
    if (fraudIndicators.length > 0) {
        await logSecurityEvent(
            SECURITY_EVENTS.PAYMENT_FRAUD_ATTEMPT,
            fraudIndicators.length > 2 ? RISK_LEVELS.HIGH : RISK_LEVELS.MEDIUM,
            {
                fraud_indicators: fraudIndicators,
                payment_data: paymentData
            },
            req
        );
    }

    return fraudIndicators;
}

/**
 * Check for API abuse patterns
 */
async function checkApiAbuse(req) {
    const ip = req.ip;
    const endpoint = req.path;
    const now = new Date();
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);

    try {
        // Check request frequency from IP
        const recentRequests = await supabase
            .from('security_audit_log')
            .select('*')
            .eq('ip_address', ip)
            .gte('timestamp', oneHourAgo.toISOString())
            .order('timestamp', { ascending: false });

        if (recentRequests.data && recentRequests.data.length > 100) {
            await logSecurityEvent(
                SECURITY_EVENTS.API_ABUSE,
                RISK_LEVELS.HIGH,
                {
                    request_count: recentRequests.data.length,
                    time_window: '1 hour'
                },
                req
            );
            return true;
        }

        // Check for endpoint-specific abuse
        const endpointRequests = recentRequests.data?.filter(r => r.endpoint === endpoint) || [];
        if (endpointRequests.length > 50) {
            await logSecurityEvent(
                SECURITY_EVENTS.API_ABUSE,
                RISK_LEVELS.MEDIUM,
                {
                    endpoint_request_count: endpointRequests.length,
                    endpoint: endpoint,
                    time_window: '1 hour'
                },
                req
            );
            return true;
        }

        return false;
    } catch (error) {
        logger.error('Error checking API abuse', { error: error.message });
        return false;
    }
}

/**
 * Security monitoring middleware
 */
function securityMonitoringMiddleware(req, res, next) {
    // Analyze request for suspicious patterns
    const suspiciousPatterns = analyzeRequest(req);
    
    if (suspiciousPatterns.length > 0) {
        logSecurityEvent(
            SECURITY_EVENTS.SUSPICIOUS_ACTIVITY,
            suspiciousPatterns.length > 2 ? RISK_LEVELS.HIGH : RISK_LEVELS.MEDIUM,
            { patterns: suspiciousPatterns },
            req
        );
    }

    // Check for API abuse
    checkApiAbuse(req);

    next();
}

module.exports = {
    SECURITY_EVENTS,
    RISK_LEVELS,
    logSecurityEvent,
    analyzeRequest,
    monitorPaymentFraud,
    checkApiAbuse,
    securityMonitoringMiddleware
};
