# Production Deployment Guide

## Overview

This guide provides step-by-step instructions for deploying the Stripe Integration system to production, including server setup, SSL configuration, database setup, and monitoring.

## Prerequisites

- **Server**: Ubuntu 20.04+ or CentOS 8+ with root access
- **Domain**: Registered domain with DNS control
- **SSL Certificate**: Let's Encrypt or commercial SSL
- **Stripe Account**: Live API keys
- **Supabase**: Production database
- **WordPress**: Production WordPress site

## Server Requirements

### Minimum Specifications
- **CPU**: 2 cores
- **RAM**: 4GB
- **Storage**: 20GB SSD
- **Bandwidth**: 100GB/month

### Recommended Specifications
- **CPU**: 4 cores
- **RAM**: 8GB
- **Storage**: 50GB SSD
- **Bandwidth**: 500GB/month

## Step 1: Server Setup

### 1.1 Initial Server Configuration

```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Install essential packages
sudo apt install -y curl wget git unzip software-properties-common

# Create application user
sudo adduser --system --group --home /opt/stripe-integration stripe-app
sudo usermod -aG sudo stripe-app
```

### 1.2 Install Node.js

```bash
# Install Node.js 18 LTS
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt install -y nodejs

# Verify installation
node --version  # Should be v18.x.x
npm --version   # Should be 9.x.x

# Install PM2 for process management
sudo npm install -g pm2
```

### 1.3 Install Nginx

```bash
# Install Nginx
sudo apt install -y nginx

# Start and enable Nginx
sudo systemctl start nginx
sudo systemctl enable nginx

# Check status
sudo systemctl status nginx
```

### 1.4 Configure Firewall

```bash
# Install and configure UFW
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
sudo ufw --force enable

# Check status
sudo ufw status
```

## Step 2: Application Deployment

### 2.1 Clone and Setup Application

```bash
# Switch to application user
sudo su - stripe-app

# Clone repository
cd /opt/stripe-integration
git clone <your-repository-url> .

# Install backend dependencies
cd backend
npm ci --production

# Create logs directory
mkdir -p logs
```

### 2.2 Environment Configuration

```bash
# Create production environment file
cd /opt/stripe-integration/backend
sudo nano .env
```

**Production .env file:**
```env
# Environment
NODE_ENV=production
PORT=3000

# Database
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your_production_service_role_key

# Stripe (LIVE KEYS)
STRIPE_SECRET_KEY=sk_live_your_live_secret_key
STRIPE_PUBLISHABLE_KEY=pk_live_your_live_publishable_key
STRIPE_WEBHOOK_SECRET=whsec_your_live_webhook_secret

# Security
JWT_SECRET=your_super_secure_jwt_secret_64_chars_minimum
API_SECRET_KEY=your_super_secure_api_secret_64_chars_minimum
WEBHOOK_SECRET=your_super_secure_webhook_secret_64_chars_minimum

# CORS
CORS_ORIGIN=https://your-wordpress-domain.com

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Security Features
ENABLE_IP_WHITELIST=false
ENABLE_SIGNATURE_VALIDATION=true
SECURITY_WEBHOOK_URL=https://your-monitoring-service.com/webhook

# Logging
LOG_LEVEL=info
ENABLE_REQUEST_LOGGING=true

# Session
SESSION_SECRET=your_super_secure_session_secret_64_chars_minimum
```

### 2.3 Set File Permissions

```bash
# Set proper ownership
sudo chown -R stripe-app:stripe-app /opt/stripe-integration

# Set secure permissions
chmod 600 /opt/stripe-integration/backend/.env
chmod -R 755 /opt/stripe-integration
```

## Step 3: Database Setup

### 3.1 Supabase Production Configuration

1. **Create Production Project:**
   - Go to https://supabase.com
   - Create new project for production
   - Choose appropriate region
   - Set strong database password

2. **Run Database Schema:**
   ```sql
   -- Copy and execute the entire schema from:
   -- backend/database/schema.sql
   ```

3. **Configure RLS Policies:**
   - Ensure Row Level Security is enabled
   - Verify all policies are properly configured
   - Test with service role key

### 3.2 Database Security

```sql
-- Create read-only user for monitoring
CREATE USER monitoring_user WITH PASSWORD 'secure_monitoring_password';
GRANT CONNECT ON DATABASE postgres TO monitoring_user;
GRANT USAGE ON SCHEMA public TO monitoring_user;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO monitoring_user;
```

## Step 4: SSL Certificate Setup

### 4.1 Install Certbot

```bash
# Install Certbot
sudo apt install -y certbot python3-certbot-nginx

# Obtain SSL certificate
sudo certbot --nginx -d your-api-domain.com

# Test automatic renewal
sudo certbot renew --dry-run
```

### 4.2 Configure Auto-Renewal

```bash
# Add cron job for automatic renewal
sudo crontab -e

# Add this line:
0 12 * * * /usr/bin/certbot renew --quiet
```

## Step 5: Nginx Configuration

### 5.1 Create Nginx Configuration

```bash
sudo nano /etc/nginx/sites-available/stripe-integration
```

**Nginx configuration:**
```nginx
upstream stripe_backend {
    server 127.0.0.1:3000;
    keepalive 32;
}

server {
    listen 80;
    server_name your-api-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-api-domain.com;

    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/your-api-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-api-domain.com/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # Security Headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header Referrer-Policy "no-referrer";

    # Rate Limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req zone=api burst=20 nodelay;

    # Logging
    access_log /var/log/nginx/stripe-integration.access.log;
    error_log /var/log/nginx/stripe-integration.error.log;

    # Main location
    location / {
        proxy_pass http://stripe_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }

    # Health check endpoint
    location /health {
        proxy_pass http://stripe_backend;
        access_log off;
    }
}
```

### 5.2 Enable Site

```bash
# Enable the site
sudo ln -s /etc/nginx/sites-available/stripe-integration /etc/nginx/sites-enabled/

# Test configuration
sudo nginx -t

# Reload Nginx
sudo systemctl reload nginx
```

## Step 6: Process Management with PM2

### 6.1 Create PM2 Configuration

```bash
# Create PM2 ecosystem file
cd /opt/stripe-integration
nano ecosystem.config.js
```

**PM2 configuration:**
```javascript
module.exports = {
  apps: [{
    name: 'stripe-integration-api',
    script: './backend/src/server.js',
    cwd: '/opt/stripe-integration',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true,
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=1024'
  }]
};
```

### 6.2 Start Application

```bash
# Start with PM2
pm2 start ecosystem.config.js

# Save PM2 configuration
pm2 save

# Setup PM2 startup script
pm2 startup
# Follow the instructions provided by the command

# Check status
pm2 status
pm2 logs
```

## Step 7: WordPress Plugin Deployment

### 7.1 Upload Plugin

```bash
# Upload plugin to WordPress server
scp -r wordpress-plugin/ user@wordpress-server:/var/www/html/wp-content/plugins/stripe-integration/

# Set proper permissions
sudo chown -R www-data:www-data /var/www/html/wp-content/plugins/stripe-integration/
sudo chmod -R 755 /var/www/html/wp-content/plugins/stripe-integration/
```

### 7.2 Configure WordPress Plugin

1. **Activate Plugin:**
   - Go to WordPress admin
   - Navigate to Plugins
   - Activate "Stripe Integration"

2. **Configure Settings:**
   - Go to Settings > Stripe Integration
   - Set API Base URL: `https://your-api-domain.com/api`
   - Enter live Stripe publishable key
   - Configure vendor settings
   - Test connection

## Step 8: Monitoring and Logging

### 8.1 Log Rotation

```bash
# Create logrotate configuration
sudo nano /etc/logrotate.d/stripe-integration
```

**Logrotate configuration:**
```
/opt/stripe-integration/logs/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 stripe-app stripe-app
    postrotate
        pm2 reload stripe-integration-api
    endscript
}
```

### 8.2 System Monitoring

```bash
# Install monitoring tools
sudo apt install -y htop iotop nethogs

# Setup basic monitoring script
nano /opt/stripe-integration/monitor.sh
```

**Monitoring script:**
```bash
#!/bin/bash
# Basic health monitoring

API_URL="https://your-api-domain.com/health"
LOG_FILE="/opt/stripe-integration/logs/monitor.log"

# Check API health
if curl -f -s $API_URL > /dev/null; then
    echo "$(date): API is healthy" >> $LOG_FILE
else
    echo "$(date): API is down - restarting" >> $LOG_FILE
    pm2 restart stripe-integration-api
fi

# Check disk space
DISK_USAGE=$(df / | tail -1 | awk '{print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "$(date): Disk usage is ${DISK_USAGE}%" >> $LOG_FILE
fi
```

```bash
# Make executable and add to cron
chmod +x /opt/stripe-integration/monitor.sh
crontab -e

# Add monitoring job (every 5 minutes)
*/5 * * * * /opt/stripe-integration/monitor.sh
```

## Step 9: Security Hardening

### 9.1 System Security

```bash
# Install fail2ban
sudo apt install -y fail2ban

# Configure fail2ban for Nginx
sudo nano /etc/fail2ban/jail.local
```

**Fail2ban configuration:**
```ini
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 5

[nginx-http-auth]
enabled = true

[nginx-limit-req]
enabled = true
filter = nginx-limit-req
action = iptables-multiport[name=ReqLimit, port="http,https", protocol=tcp]
logpath = /var/log/nginx/stripe-integration.error.log
maxretry = 10
findtime = 600
bantime = 7200
```

### 9.2 Application Security

```bash
# Set up automatic security updates
sudo apt install -y unattended-upgrades
sudo dpkg-reconfigure -plow unattended-upgrades

# Configure SSH security
sudo nano /etc/ssh/sshd_config
```

**SSH security settings:**
```
PermitRootLogin no
PasswordAuthentication no
PubkeyAuthentication yes
Port 2222  # Change default port
```

## Step 10: Backup Strategy

### 10.1 Database Backup

```bash
# Create backup script
nano /opt/stripe-integration/backup.sh
```

**Backup script:**
```bash
#!/bin/bash
BACKUP_DIR="/opt/stripe-integration/backups"
DATE=$(date +%Y%m%d_%H%M%S)

# Create backup directory
mkdir -p $BACKUP_DIR

# Backup application files
tar -czf $BACKUP_DIR/app_$DATE.tar.gz /opt/stripe-integration --exclude=node_modules --exclude=logs

# Backup database (Supabase handles this automatically)
# But you can export specific data if needed

# Clean old backups (keep 30 days)
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete

echo "Backup completed: $DATE"
```

### 10.2 Automated Backups

```bash
# Make executable and schedule
chmod +x /opt/stripe-integration/backup.sh
crontab -e

# Daily backup at 2 AM
0 2 * * * /opt/stripe-integration/backup.sh
```

## Step 11: Testing Production Deployment

### 11.1 Health Checks

```bash
# Test API health
curl https://your-api-domain.com/health

# Test SSL
curl -I https://your-api-domain.com/api

# Test rate limiting
for i in {1..15}; do curl https://your-api-domain.com/api; done
```

### 11.2 WordPress Integration Test

1. Create test payment form
2. Process test payment with live Stripe test keys
3. Verify commission calculation
4. Check security logs

## Step 12: Go Live Checklist

### Pre-Launch
- [ ] SSL certificate installed and working
- [ ] Live Stripe keys configured
- [ ] Database properly configured
- [ ] All environment variables set
- [ ] Security headers configured
- [ ] Rate limiting working
- [ ] Monitoring setup
- [ ] Backups configured
- [ ] WordPress plugin configured
- [ ] Test payments successful

### Post-Launch
- [ ] Monitor error logs
- [ ] Check payment processing
- [ ] Verify commission calculations
- [ ] Monitor performance metrics
- [ ] Test security features
- [ ] Verify backup systems

## Troubleshooting

### Common Issues

**API not responding:**
```bash
# Check PM2 status
pm2 status
pm2 logs

# Check Nginx
sudo nginx -t
sudo systemctl status nginx
```

**SSL issues:**
```bash
# Check certificate
sudo certbot certificates

# Renew if needed
sudo certbot renew
```

**Database connection issues:**
- Verify Supabase credentials
- Check network connectivity
- Review RLS policies

## Support and Maintenance

### Regular Maintenance Tasks
- Weekly log review
- Monthly security updates
- Quarterly performance review
- Annual security audit

### Emergency Contacts
- System Administrator: <EMAIL>
- Security Team: <EMAIL>
- Stripe Support: https://support.stripe.com

---

**Deployment Complete! 🚀**

Your Stripe Integration system is now live and ready for production use.
